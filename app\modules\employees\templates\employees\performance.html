{% extends "employees/base_hr.html" %}

{% block title %}Performance - {{ employee.full_name }}{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-star me-2"></i>Performance de {{ employee.full_name }}
    </h1>
    <div>
        <a href="{{ url_for('employees.new_performance', id=employee.id) }}" class="btn btn-primary me-2">
            <i class="fas fa-plus me-1"></i>Nouvelle Évaluation
        </a>
        <a href="{{ url_for('employees.detail', id=employee.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour
        </a>
    </div>
</div>

<!-- Statistiques -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ performances|length }}</h4>
                        <p class="mb-0">Évaluations</p>
                    </div>
                    <i class="fas fa-clipboard-list fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ "%.1f"|format(avg_score or 0) }}/5</h4>
                        <p class="mb-0">Score Moyen</p>
                    </div>
                    <i class="fas fa-star fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ finalized_count }}</h4>
                        <p class="mb-0">Finalisées</p>
                    </div>
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ acknowledged_count }}</h4>
                        <p class="mb-0">Reconnues</p>
                    </div>
                    <i class="fas fa-user-check fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Graphique radar des compétences -->
{% if performances %}
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-radar me-2"></i>Profil de Compétences
                </h5>
            </div>
            <div class="card-body">
                <canvas id="skillsChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>Évolution du Score
                </h5>
            </div>
            <div class="card-body">
                <canvas id="evolutionChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Liste des évaluations -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>Historique des Évaluations
        </h5>
    </div>
    <div class="card-body p-0">
        {% if performances %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Date</th>
                        <th>Période</th>
                        <th>Score Global</th>
                        <th>Ponctualité</th>
                        <th>Qualité</th>
                        <th>Équipe</th>
                        <th>Communication</th>
                        <th>Initiative</th>
                        <th>Service Client</th>
                        <th>Statut</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for performance in performances %}
                    <tr>
                        <td>
                            <strong>{{ performance.evaluation_date | datetime_auto('date') }}</strong>
                        </td>
                        <td>
                            <span class="badge bg-info">
                                {{ performance.evaluation_period_start | datetime_auto('full') if performance.evaluation_period_start else 'Non définie' }} - {{ performance.evaluation_period_end | datetime_auto('date') if performance.evaluation_period_end else '' }}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if (performance.overall_score or 0) >= 4 else 'warning' if (performance.overall_score or 0) >= 3 else 'danger' }} fs-6">
                                {{ "%.1f"|format(performance.overall_score or 0) }}/5
                            </span>
                        </td>
                        <td>{{ performance.punctuality_score or '-' }}/5</td>
                        <td>{{ performance.quality_of_work_score or '-' }}/5</td>
                        <td>{{ performance.teamwork_score or '-' }}/5</td>
                        <td>{{ performance.communication_score or '-' }}/5</td>
                        <td>{{ performance.initiative_score or '-' }}/5</td>
                        <td>{{ performance.customer_service_score or '-' }}/5</td>
                        <td>
                            {% if performance.is_finalized %}
                                {% if performance.employee_acknowledged %}
                                    <span class="badge bg-success">Reconnue</span>
                                {% else %}
                                    <span class="badge bg-warning">Finalisée</span>
                                {% endif %}
                            {% else %}
                                <span class="badge bg-secondary">Brouillon</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('employees.performance_detail', performance_id=performance.id) }}" 
                                   class="btn btn-sm btn-outline-info" title="Voir">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('employees.edit_performance', performance_id=performance.id) }}" 
                                   class="btn btn-sm btn-outline-warning" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-success" 
                                        onclick="downloadEvaluation({{ performance.id }})" title="Télécharger">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-star fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucune évaluation</h5>
            <p class="text-muted">Créez la première évaluation pour cet employé.</p>
            <a href="{{ url_for('employees.new_performance', id=employee.id) }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Créer une Évaluation
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Objectifs et commentaires récents -->
{% if performances %}
<div class="row mt-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bullseye me-2"></i>Derniers Objectifs
                </h5>
            </div>
            <div class="card-body">
                {% set latest_performance = performances[0] %}
                {% if latest_performance.goals %}
                    <p>{{ latest_performance.goals }}</p>
                    <small class="text-muted">Définis le {{ latest_performance.evaluation_date | datetime_auto('date') }}</small>
                {% else %}
                    <p class="text-muted">Aucun objectif défini dans la dernière évaluation.</p>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-comments me-2"></i>Derniers Commentaires
                </h5>
            </div>
            <div class="card-body">
                {% set latest_performance = performances[0] %}
                {% if latest_performance.comments %}
                    <p>{{ latest_performance.comments }}</p>
                    <small class="text-muted">Ajoutés le {{ latest_performance.evaluation_date | datetime_auto('date') }}</small>
                {% else %}
                    <p class="text-muted">Aucun commentaire dans la dernière évaluation.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
{% if performances %}
// Graphique radar des compétences (dernière évaluation)
{% set latest = performances[0] %}
const skillsCtx = document.getElementById('skillsChart').getContext('2d');
const skillsChart = new Chart(skillsCtx, {
    type: 'radar',
    data: {
        labels: ['Ponctualité', 'Qualité', 'Équipe', 'Communication', 'Initiative', 'Service Client'],
        datasets: [{
            label: 'Dernière Évaluation',
            data: [
                {{ latest.punctuality_score or 0 }},
                {{ latest.quality_of_work_score or 0 }},
                {{ latest.teamwork_score or 0 }},
                {{ latest.communication_score or 0 }},
                {{ latest.initiative_score or 0 }},
                {{ latest.customer_service_score or 0 }}
            ],
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            r: {
                beginAtZero: true,
                max: 5
            }
        }
    }
});

// Graphique d'évolution du score
const evolutionCtx = document.getElementById('evolutionChart').getContext('2d');
const evolutionChart = new Chart(evolutionCtx, {
    type: 'line',
    data: {
        labels: [
            {% for performance in performances|reverse %}
            '{{ performance.evaluation_date | datetime_auto('full') }} }}'{{ ',' if not loop.last }}
            {% endfor %}
        ],
        datasets: [{
            label: 'Score Global',
            data: [
                {% for performance in performances|reverse %}
                {{ performance.overall_score or 0 }}{{ ',' if not loop.last }}
                {% endfor %}
            ],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 5,
                ticks: {
                    callback: function(value) {
                        return value + '/5';
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': ' + context.parsed.y + '/5';
                    }
                }
            }
        }
    }
});
{% endif %}

function downloadEvaluation(performanceId) {
    const url = `/employees/performance/${performanceId}/download`;
    window.open(url, '_blank');
}
</script>
{% endblock %}

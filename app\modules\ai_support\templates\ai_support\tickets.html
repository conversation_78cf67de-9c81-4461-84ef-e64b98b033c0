{% extends "ai_support/base.html" %}

{% block support_content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Mes tickets de support</h2>
            <a href="{{ url_for('ai_support.create_ticket') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouveau ticket
            </a>
        </div>
    </div>
</div>

<!-- Filtres -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="status" class="form-label">Statut</label>
                        <select name="status" id="status" class="form-select">
                            <option value="">Tous les statuts</option>
                            <option value="open" {% if request.args.get('status') == 'open' %}selected{% endif %}>Ouvert</option>
                            <option value="in_progress" {% if request.args.get('status') == 'in_progress' %}selected{% endif %}>En cours</option>
                            <option value="waiting_ai" {% if request.args.get('status') == 'waiting_ai' %}selected{% endif %}>En attente IA</option>
                            <option value="waiting_human" {% if request.args.get('status') == 'waiting_human' %}selected{% endif %}>En attente agent</option>
                            <option value="resolved" {% if request.args.get('status') == 'resolved' %}selected{% endif %}>Résolu</option>
                            <option value="closed" {% if request.args.get('status') == 'closed' %}selected{% endif %}>Fermé</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="category" class="form-label">Catégorie</label>
                        <select name="category" id="category" class="form-select">
                            <option value="">Toutes les catégories</option>
                            <option value="technical" {% if request.args.get('category') == 'technical' %}selected{% endif %}>Technique</option>
                            <option value="billing" {% if request.args.get('category') == 'billing' %}selected{% endif %}>Facturation</option>
                            <option value="general" {% if request.args.get('category') == 'general' %}selected{% endif %}>Général</option>
                            <option value="feature_request" {% if request.args.get('category') == 'feature_request' %}selected{% endif %}>Demande de fonctionnalité</option>
                            <option value="bug_report" {% if request.args.get('category') == 'bug_report' %}selected{% endif %}>Rapport de bug</option>
                            <option value="training" {% if request.args.get('category') == 'training' %}selected{% endif %}>Formation</option>
                            <option value="integration" {% if request.args.get('category') == 'integration' %}selected{% endif %}>Intégration</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-filter"></i> Filtrer
                            </button>
                            <a href="{{ url_for('ai_support.tickets') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Effacer
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Liste des tickets -->
{% if tickets.items %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Numéro</th>
                                <th>Titre</th>
                                <th>Statut</th>
                                <th>Priorité</th>
                                <th>Catégorie</th>
                                <th>Créé le</th>
                                <th>Mis à jour</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for ticket in tickets.items %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('ai_support.view_ticket', id=ticket.id) }}" 
                                       class="text-decoration-none fw-bold">
                                        {{ ticket.ticket_number }}
                                    </a>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="{{ ticket.title }}">
                                        {{ ticket.title }}
                                    </div>
                                </td>
                                <td>
                                    <span class="ticket-status status-{{ ticket.status.value }}">
                                        {{ ticket.status.value.replace('_', ' ').title() }}
                                    </span>
                                </td>
                                <td>
                                    <span class="priority-{{ ticket.priority.value }}">
                                        <i class="fas fa-flag"></i> {{ ticket.priority.value.title() }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">
                                        {{ ticket.category.value.replace('_', ' ').title() }}
                                    </span>
                                </td>
                                <td>{{ ticket.created_at | datetime_auto('full') }}                             <td>{{ ticket.updated_at | datetime_auto('full') }}                             <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('ai_support.view_ticket', id=ticket.id) }}" 
                                           class="btn btn-outline-primary" title="Voir">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if ticket.status.value not in ['resolved', 'closed'] %}
                                        <button class="btn btn-outline-warning" 
                                                onclick="escalateTicket({{ ticket.id }})" 
                                                title="Escalader">
                                            <i class="fas fa-user"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pagination -->
{% if tickets.pages > 1 %}
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="Navigation des tickets">
            <ul class="pagination justify-content-center">
                {% if tickets.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('ai_support.tickets', page=tickets.prev_num, **request.args) }}">
                        <i class="fas fa-chevron-left"></i> Précédent
                    </a>
                </li>
                {% endif %}
                
                {% for page_num in tickets.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != tickets.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('ai_support.tickets', page=page_num, **request.args) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if tickets.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('ai_support.tickets', page=tickets.next_num, **request.args) }}">
                        Suivant <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>
{% endif %}

{% else %}
<!-- Aucun ticket -->
<div class="row">
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-ticket-alt fa-4x text-muted mb-3"></i>
            <h4>Aucun ticket trouvé</h4>
            <p class="text-muted">
                {% if request.args.get('status') or request.args.get('category') %}
                Aucun ticket ne correspond aux filtres sélectionnés.
                {% else %}
                Vous n'avez pas encore créé de ticket de support.
                {% endif %}
            </p>
            <div class="mt-3">
                {% if request.args.get('status') or request.args.get('category') %}
                <a href="{{ url_for('ai_support.tickets') }}" class="btn btn-outline-primary me-2">
                    <i class="fas fa-times"></i> Effacer les filtres
                </a>
                {% endif %}
                <a href="{{ url_for('ai_support.create_ticket') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Créer un ticket
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
{{ super() }}
<script>
function escalateTicket(ticketId) {
    const reason = prompt('Pourquoi souhaitez-vous escalader ce ticket vers un agent humain ?');
    if (!reason) return;
    
    $.ajax({
        url: `/support/api/tickets/${ticketId}/escalate`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            reason: reason,
            priority: 'high'
        }),
        success: function(response) {
            if (response.success) {
                alert('Ticket escaladé avec succès !');
                location.reload();
            } else {
                alert('Erreur lors de l\'escalade du ticket');
            }
        },
        error: function() {
            alert('Erreur de connexion');
        }
    });
}
</script>
{% endblock %}

{% extends "ai_support/base.html" %}

{% block support_content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Base de connaissances</h2>
            {% if current_user.is_admin %}
            <a href="{{ url_for('ai_support.create_article') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouvel article
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Barre de recherche -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" name="q" class="form-control" 
                                   placeholder="Rechercher dans la base de connaissances..." 
                                   value="{{ request.args.get('q', '') }}">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select name="category" class="form-select">
                            <option value="">Toutes les catégories</option>
                            <option value="technical" {% if request.args.get('category') == 'technical' %}selected{% endif %}>Technique</option>
                            <option value="billing" {% if request.args.get('category') == 'billing' %}selected{% endif %}>Facturation</option>
                            <option value="general" {% if request.args.get('category') == 'general' %}selected{% endif %}>Général</option>
                            <option value="training" {% if request.args.get('category') == 'training' %}selected{% endif %}>Formation</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('ai_support.knowledge_base') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-times"></i> Effacer
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Articles -->
{% if articles.items %}
<div class="row">
    {% for article in articles.items %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span class="badge bg-secondary">
                    {{ article.category.value.replace('_', ' ').title() }}
                </span>
                <small class="text-muted">
                    <i class="fas fa-eye"></i> {{ article.usage_count }}
                </small>
            </div>
            <div class="card-body">
                <h6 class="card-title">{{ article.title }}</h6>
                <p class="card-text text-muted">
                    {{ article.content[:150] }}{% if article.content|length > 150 %}...{% endif %}
                </p>
                {% if article.tags %}
                <div class="mb-2">
                    {% for tag in article.get_tags_list()[:3] %}
                    <span class="badge bg-light text-dark me-1">#{{ tag }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        {{ article.created_at | datetime_auto('date') }}
                    </small>
                    <a href="{{ url_for('ai_support.view_article', id=article.id) }}" 
                       class="btn btn-sm btn-outline-primary">
                        Lire <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if articles.pages > 1 %}
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="Navigation des articles">
            <ul class="pagination justify-content-center">
                {% if articles.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('ai_support.knowledge_base', page=articles.prev_num, **request.args) }}">
                        <i class="fas fa-chevron-left"></i> Précédent
                    </a>
                </li>
                {% endif %}
                
                {% for page_num in articles.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != articles.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('ai_support.knowledge_base', page=page_num, **request.args) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if articles.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('ai_support.knowledge_base', page=articles.next_num, **request.args) }}">
                        Suivant <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>
{% endif %}

{% else %}
<!-- Aucun article -->
<div class="row">
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-book fa-4x text-muted mb-3"></i>
            <h4>Aucun article trouvé</h4>
            <p class="text-muted">
                {% if request.args.get('q') or request.args.get('category') %}
                Aucun article ne correspond à votre recherche.
                {% else %}
                La base de connaissances est vide pour le moment.
                {% endif %}
            </p>
            <div class="mt-3">
                {% if request.args.get('q') or request.args.get('category') %}
                <a href="{{ url_for('ai_support.knowledge_base') }}" class="btn btn-outline-primary me-2">
                    <i class="fas fa-times"></i> Effacer la recherche
                </a>
                {% endif %}
                <a href="{{ url_for('ai_support.chat') }}" class="btn btn-primary">
                    <i class="fas fa-comments"></i> Poser une question au chat
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Articles populaires -->
<div class="row mt-5">
    <div class="col-12">
        <h4>Articles les plus consultés</h4>
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">Comment utiliser la caisse ?</h6>
                        <p class="card-text text-muted">Guide complet pour utiliser le système de caisse...</p>
                        <a href="#" class="btn btn-sm btn-outline-primary">Lire</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">Gestion de l'inventaire</h6>
                        <p class="card-text text-muted">Apprenez à gérer vos stocks et produits...</p>
                        <a href="#" class="btn btn-sm btn-outline-primary">Lire</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">Générer des rapports</h6>
                        <p class="card-text text-muted">Comment créer et exporter vos rapports de vente...</p>
                        <a href="#" class="btn btn-sm btn-outline-primary">Lire</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

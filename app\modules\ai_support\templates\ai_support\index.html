{% extends "ai_support/base.html" %}

{% block support_content %}
<div class="row">
    <div class="col-12">
        <h2>Support AI - Tableau de bord</h2>
        <p class="text-muted">Bienvenue dans votre espace de support automatisé. Notre IA est là pour vous aider 24h/24 et 7j/7.</p>
    </div>
</div>

<!-- Statistiques -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ stats.total_tickets }}</h5>
                <p class="card-text">Total tickets</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ stats.open_tickets }}</h5>
                <p class="card-text">Tickets ouverts</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">{{ stats.resolved_tickets }}</h5>
                <p class="card-text">Tickets résolus</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">{{ stats.active_conversations }}</h5>
                <p class="card-text">Conversations actives</p>
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="row mb-4">
    <div class="col-12">
        <h4>Actions rapides</h4>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-comments fa-3x text-primary mb-3"></i>
                <h5 class="card-title">Chat en direct</h5>
                <p class="card-text">Discutez directement avec notre IA pour une assistance immédiate.</p>
                <a href="{{ url_for('ai_support.chat') }}" class="btn btn-primary">Démarrer le chat</a>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-plus fa-3x text-success mb-3"></i>
                <h5 class="card-title">Nouveau ticket</h5>
                <p class="card-text">Créez un ticket pour un problème complexe nécessitant un suivi.</p>
                <a href="{{ url_for('ai_support.create_ticket') }}" class="btn btn-success">Créer un ticket</a>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-book fa-3x text-info mb-3"></i>
                <h5 class="card-title">Base de connaissances</h5>
                <p class="card-text">Consultez notre documentation et nos guides d'utilisation.</p>
                <a href="{{ url_for('ai_support.knowledge_base') }}" class="btn btn-info">Parcourir</a>
            </div>
        </div>
    </div>
</div>

<!-- Tickets récents -->
{% if recent_tickets %}
<div class="row">
    <div class="col-12">
        <h4>Vos tickets récents</h4>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Numéro</th>
                        <th>Titre</th>
                        <th>Statut</th>
                        <th>Priorité</th>
                        <th>Créé le</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for ticket in recent_tickets %}
                    <tr>
                        <td>
                            <a href="{{ url_for('ai_support.view_ticket', id=ticket.id) }}" class="text-decoration-none">
                                {{ ticket.ticket_number }}
                            </a>
                        </td>
                        <td>{{ ticket.title[:50] }}{% if ticket.title|length > 50 %}...{% endif %}</td>
                        <td>
                            <span class="ticket-status status-{{ ticket.status.value }}">
                                {{ ticket.status.value.replace('_', ' ').title() }}
                            </span>
                        </td>
                        <td>
                            <span class="priority-{{ ticket.priority.value }}">
                                <i class="fas fa-flag"></i> {{ ticket.priority.value.title() }}
                            </span>
                        </td>
                        <td>{{ ticket.created_at | datetime_auto('full') }}                     <td>
                            <a href="{{ url_for('ai_support.view_ticket', id=ticket.id) }}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i> Voir
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="text-center mt-3">
            <a href="{{ url_for('ai_support.tickets') }}" class="btn btn-outline-primary">
                Voir tous mes tickets
            </a>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-info text-center">
            <h5><i class="fas fa-info-circle"></i> Aucun ticket pour le moment</h5>
            <p>Vous n'avez pas encore créé de ticket de support. Notre IA est prête à vous aider !</p>
            <a href="{{ url_for('ai_support.create_ticket') }}" class="btn btn-primary">
                Créer votre premier ticket
            </a>
        </div>
    </div>
</div>
{% endif %}

<!-- Conseils d'utilisation -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-lightbulb"></i> Conseils d'utilisation</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Chat en direct</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> Idéal pour des questions rapides</li>
                            <li><i class="fas fa-check text-success"></i> Réponses instantanées de l'IA</li>
                            <li><i class="fas fa-check text-success"></i> Disponible 24h/24</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Tickets de support</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> Pour des problèmes complexes</li>
                            <li><i class="fas fa-check text-success"></i> Suivi et historique complets</li>
                            <li><i class="fas fa-check text-success"></i> Escalade vers un agent si nécessaire</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

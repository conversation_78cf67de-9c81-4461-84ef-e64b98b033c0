{% extends "ai_support/base.html" %}

{% block support_content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ url_for('ai_support.index') }}">Support AI</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{{ url_for('ai_support.knowledge_base') }}">Base de connaissances</a>
                </li>
                <li class="breadcrumb-item active">{{ article.title }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">{{ article.title }}</h4>
                        <div class="d-flex align-items-center gap-3">
                            <span class="badge bg-secondary">
                                {{ article.category.value.replace('_', ' ').title() }}
                            </span>
                            <small class="text-muted">
                                <i class="fas fa-eye"></i> {{ article.usage_count }} vues
                            </small>
                            <small class="text-muted">
                                <i class="fas fa-calendar"></i> {{ article.created_at | datetime_auto('date') }}
                            </small>
                        </div>
                    </div>
                    {% if current_user.is_admin %}
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" 
                                data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-edit"></i> Modifier
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item text-danger" href="#" 
                                   onclick="deleteArticle({{ article.id }})">
                                    <i class="fas fa-trash"></i> Supprimer
                                </a>
                            </li>
                        </ul>
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <div class="article-content">
                    {{ article.content|nl2br|safe }}
                </div>
                
                {% if article.tags %}
                <hr>
                <div class="article-tags">
                    <h6>Tags :</h6>
                    {% for tag in article.get_tags_list() %}
                    <span class="badge bg-light text-dark me-1">#{{ tag }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        Créé par {{ article.created_by.username }} le {{ article.created_at | datetime_auto('full') }}                    {% if article.updated_at != article.created_at %}
                        <br>Mis à jour le {{ article.updated_at | datetime_auto('full') }}                    {% endif %}
                    </small>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary btn-sm" onclick="rateArticle(5)">
                            <i class="fas fa-thumbs-up"></i> Utile
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="rateArticle(1)">
                            <i class="fas fa-thumbs-down"></i> Pas utile
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Actions -->
        <div class="mt-3">
            <a href="{{ url_for('ai_support.knowledge_base') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Retour à la base de connaissances
            </a>
            <a href="{{ url_for('ai_support.chat') }}" class="btn btn-primary">
                <i class="fas fa-comments"></i> Poser une question
            </a>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Articles similaires -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Articles similaires</h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="#" class="list-group-item list-group-item-action">
                        <h6 class="mb-1">Configuration de la caisse</h6>
                        <small class="text-muted">Guide de configuration initiale</small>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <h6 class="mb-1">Résolution des problèmes</h6>
                        <small class="text-muted">Solutions aux problèmes courants</small>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <h6 class="mb-1">Mise à jour du système</h6>
                        <small class="text-muted">Comment mettre à jour votre POS</small>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Aide rapide -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Besoin d'aide ?</h6>
            </div>
            <div class="card-body">
                <p class="card-text">Cet article ne répond pas à votre question ?</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('ai_support.chat') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-comments"></i> Chat en direct
                    </a>
                    <a href="{{ url_for('ai_support.create_ticket') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-ticket-alt"></i> Créer un ticket
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Statistiques -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Statistiques</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h5 class="text-primary">{{ article.usage_count }}</h5>
                            <small class="text-muted">Vues</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h5 class="text-success">95%</h5>
                        <small class="text-muted">Utile</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ super() }}
<script>
function rateArticle(rating) {
    // Simuler une évaluation d'article
    const message = rating >= 4 ? 'Merci pour votre évaluation positive !' : 'Merci pour votre retour. Nous allons améliorer cet article.';
    
    // Ici, vous pourriez envoyer une requête AJAX pour enregistrer l'évaluation
    alert(message);
}

function deleteArticle(articleId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet article ?')) {
        // Ici, vous pourriez envoyer une requête AJAX pour supprimer l'article
        alert('Fonctionnalité de suppression à implémenter');
    }
}

// Améliorer l'affichage du contenu
$(document).ready(function() {
    // Ajouter des classes Bootstrap aux éléments du contenu
    $('.article-content h1, .article-content h2, .article-content h3').addClass('mt-4 mb-3');
    $('.article-content p').addClass('mb-3');
    $('.article-content ul, .article-content ol').addClass('mb-3');
    $('.article-content code').addClass('bg-light p-1 rounded');
    
    // Ajouter des liens d'ancrage aux titres
    $('.article-content h1, .article-content h2, .article-content h3').each(function() {
        const id = $(this).text().toLowerCase().replace(/[^a-z0-9]/g, '-');
        $(this).attr('id', id);
        $(this).append(`<a href="#${id}" class="text-decoration-none text-muted ms-2">#</a>`);
    });
});
</script>
{% endblock %}

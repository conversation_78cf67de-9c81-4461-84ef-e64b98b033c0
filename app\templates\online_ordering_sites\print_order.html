<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Commande #{{ order.order_number }}</title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .order-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .customer-info, .order-details {
            width: 48%;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .items-table th, .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .items-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        
        .total-section {
            text-align: right;
            margin-top: 20px;
        }
        
        .total-line {
            margin: 5px 0;
        }
        
        .total-final {
            font-weight: bold;
            font-size: 14px;
            border-top: 2px solid #000;
            padding-top: 5px;
        }
        
        .notes {
            margin-top: 20px;
            padding: 10px;
            background-color: #f9f9f9;
            border-left: 4px solid #007bff;
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">
        <i class="fas fa-print"></i> Imprimer
    </button>

    <div class="header">
        <h1>{{ order.site.site_name }}</h1>
        <h2>Commande #{{ order.order_number }}</h2>
        <p>{{ order.ordered_at | datetime_auto('full') }}    </div>

    <div class="order-info">
        <div class="customer-info">
            <h3>Informations Client</h3>
            <p><strong>Nom:</strong> {{ order.customer.first_name }} {{ order.customer.last_name }}</p>
            {% if order.customer.phone %}
                <p><strong>Téléphone:</strong> {{ order.customer.phone }}</p>
            {% endif %}
            {% if order.customer.email %}
                <p><strong>Email:</strong> {{ order.customer.email }}</p>
            {% endif %}
            
            {% if order.order_type.value == 'delivery' and order.delivery_address %}
                <p><strong>Adresse de livraison:</strong><br>{{ order.delivery_address }}</p>
            {% endif %}
        </div>

        <div class="order-details">
            <h3>Détails de la Commande</h3>
            <p><strong>Type:</strong> 
                {% if order.order_type.value == 'delivery' %}
                    Livraison à domicile
                {% elif order.order_type.value == 'pickup' %}
                    À emporter
                {% elif order.order_type.value == 'dine_in' %}
                    Sur place
                {% elif order.order_type.value == 'drive_through' %}
                    Drive
                {% endif %}
            </p>
            <p><strong>Statut:</strong> 
                {% if order.status.value == 'pending' %}
                    En attente
                {% elif order.status.value == 'confirmed' %}
                    Confirmée
                {% elif order.status.value == 'preparing' %}
                    En préparation
                {% elif order.status.value == 'ready' %}
                    Prête
                {% elif order.status.value == 'out_for_delivery' %}
                    En livraison
                {% elif order.status.value == 'delivered' %}
                    Livrée
                {% elif order.status.value == 'cancelled' %}
                    Annulée
                {% endif %}
            </p>
            <p><strong>Paiement:</strong> 
                {% if order.payment_method.value == 'cash_on_delivery' %}
                    Espèces à la livraison
                {% elif order.payment_method.value == 'card_on_delivery' %}
                    Carte à la livraison
                {% elif order.payment_method.value == 'online_payment' %}
                    Paiement en ligne
                {% endif %}
            </p>
        </div>
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>Article</th>
                <th>Quantité</th>
                <th>Prix unitaire</th>
                <th>Total</th>
            </tr>
        </thead>
        <tbody>
            {% for item in order.items %}
            <tr>
                <td>{{ item.product.name }}</td>
                <td>{{ item.quantity }}</td>
                <td>{{ "%.2f"|format(item.unit_price) }}€</td>
                <td>{{ "%.2f"|format(item.total_price) }}€</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <div class="total-section">
        <div class="total-line">
            <strong>Sous-total: {{ "%.2f"|format(order.subtotal) }}€</strong>
        </div>
        {% if order.delivery_fee > 0 %}
        <div class="total-line">
            Frais de livraison: {{ "%.2f"|format(order.delivery_fee) }}€
        </div>
        {% endif %}
        {% if order.discount_amount > 0 %}
        <div class="total-line">
            Remise: -{{ "%.2f"|format(order.discount_amount) }}€
        </div>
        {% endif %}
        <div class="total-line total-final">
            TOTAL: {{ "%.2f"|format(order.total_amount) }}€
        </div>
    </div>

    {% if order.customer_notes %}
    <div class="notes">
        <h4>Notes du client:</h4>
        <p>{{ order.customer_notes }}</p>
    </div>
    {% endif %}

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>

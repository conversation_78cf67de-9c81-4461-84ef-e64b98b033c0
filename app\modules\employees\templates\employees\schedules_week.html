{% extends "employees/base_hr.html" %}

{% block title %}Planning de la Semaine{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-calendar-week me-2"></i>Planning de la Semaine
        <small class="text-muted">{{ start_of_week | datetime_auto('full') }} }} - {{ end_of_week | datetime_auto('date') }}</small>
    </h1>
    <a href="{{ url_for('employees.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>Retour
    </a>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-calendar me-2"></i>Planning Hebdomadaire
        </h5>
    </div>
    <div class="card-body p-0">
        {% if schedules %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Employé</th>
                        <th>Lundi</th>
                        <th>Mardi</th>
                        <th>Mercredi</th>
                        <th>Jeudi</th>
                        <th>Vendredi</th>
                        <th>Samedi</th>
                        <th>Dimanche</th>
                    </tr>
                </thead>
                <tbody>
                    {% set employees = schedules|groupby('employee.id') %}
                    {% for employee_id, employee_schedules in employees %}
                    {% set employee = employee_schedules[0].employee %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                    {{ employee.first_name[0] }}{{ employee.last_name[0] }}
                                </div>
                                <div>
                                    <strong>{{ employee.full_name }}</strong><br>
                                    <small class="text-muted">{{ employee.position }}</small>
                                </div>
                            </div>
                        </td>
                        {% for day in range(7) %}
                        <td>
                            {% set day_schedules = employee_schedules|selectattr('day_of_week', 'equalto', day)|list %}
                            {% if day_schedules %}
                                {% for schedule in day_schedules %}
                                <div class="small">
                                    <span class="badge bg-primary">{{ schedule.start_time | datetime_auto('time') }} }} - {{ schedule.end_time | datetime_auto('time') }} }}</span><br>
                                    <small class="text-muted">{{ schedule.shift_type.replace('_', ' ').title() }}</small>
                                </div>
                                {% endfor %}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-calendar-week fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucun planning pour cette semaine</h5>
            <p class="text-muted">Les plannings apparaîtront ici une fois créés.</p>
        </div>
        {% endif %}
    </div>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
    font-weight: bold;
}
</style>
{% endblock %}

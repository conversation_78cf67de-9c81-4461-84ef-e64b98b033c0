{% extends "base.html" %}

{% block styles %}
<style>
.chart-container {
    position: relative;
    height: 400px;
    margin: 20px 0;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête avec le titre et le sélecteur de période -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            {{ title }} 
            <span class="h5 text-gray-600">
                {% if period == 'today' %}
                    - Aujourd'hui
                {% elif period == 'week' %}
                    - Cette semaine
                {% elif period == 'month' %}
                    - Ce mois
                {% elif period == 'year' %}
                    - Cette année
                {% endif %}
            </span>
        </h1>
        <div class="d-flex">
            <form id="period-form" action="{{ url_for('reports.profit_loss') }}" method="GET" class="d-flex">
                <select name="period" id="period-select" class="form-control mr-2">
                    <option value="today" {% if period == 'today' %}selected{% endif %}>Aujourd'hui</option>
                    <option value="week" {% if period == 'week' %}selected{% endif %}>Cette semaine</option>
                    <option value="month" {% if period == 'month' %}selected{% endif %}>Ce mois</option>
                    <option value="year" {% if period == 'year' %}selected{% endif %}>Cette année</option>
                    <option value="custom" {% if period == 'custom' %}selected{% endif %}>Personnalisé</option>
                </select>
                <div id="custom-date-range" class="d-flex mr-2 {% if period != 'custom' %}d-none{% endif %}">
                    <input type="date" name="start_date" id="start_date" class="form-control mr-2" 
                           value="{{ start_date | datetime_auto('date') if start_date else '' }}">
                    <input type="date" name="end_date" id="end_date" class="form-control" 
                           value="{{ end_date | datetime_auto('date') if end_date else '' }}">
                </div>
                <button type="submit" class="btn btn-primary btn-sm">Appliquer</button>
            </form>
        </div>
    </div>

    <!-- Cartes des indicateurs clés -->
    <div class="row">
        <!-- Chiffre d'affaires -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Chiffre d'affaires</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ format_currency(total_sales) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Coût des produits vendus -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Coût des ventes</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ format_currency(total_cost) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- REX -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Résultat d'exploitation (REX)</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ format_currency(rex) }}
                                <span class="text-xs ml-1 text-{{ 'success' if rex >= 0 else 'danger' }}">
                                    {% if total_sales > 0 %}
                                    ({{ "%.1f"|format(rex_percentage) }}%)
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total des charges -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Total des charges</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ format_currency(total_expenses) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- RNET -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Résultat Net (RNET)</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ format_currency(rnet) }}
                                <span class="text-xs ml-1 text-{{ 'success' if rnet >= 0 else 'danger' }}">
                                    {% if total_sales > 0 %}
                                    ({{ "%.1f"|format(rnet_percentage) }}%)
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-piggy-bank fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vue d'ensemble -->
    <div class="row">
        <div class="col-md-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar me-1"></i>
                        Vue d'ensemble
                    </h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="overviewChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques -->
    <div class="row">
        <!-- Graphique d'évolution -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Évolution des résultats</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="resultsChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graphique des dépenses -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Répartition des dépenses</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="expensesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphique de progression -->
    <div class="row">
        <div class="col-md-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line me-1"></i>
                        Progression des indicateurs
                    </h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="progressionChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
<div class="row">
    <footer class="footer mt-4 py-3 bg-light">
        <div class="container">
            <div class="row">
                <!-- Company Info -->
                <div class="col-md-4 mb-3">
                    <h5>NEW POS System</h5>
                    <p class="text-muted">Version 1.0.0</p>
                    <p>Développé par Youssef Yaakoubi</p>
                </div>

                <!-- Contact -->
                <div class="col-md-4 mb-3">
                    <h5>Contact</h5>
                    <p>Youssef Yaakoubi</p>
                    <p><a href="#" class="text-decoration-none">FAQ</a></p>
                </div>

                <!-- Legal -->
                <div class="col-md-4 mb-3">
                    <h5>Informations légales</h5>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-decoration-none">Mentions légales</a></li>
                        <li><a href="#" class="text-decoration-none">Politique de confidentialité</a></li>
                        <li><a href="#" class="text-decoration-none">Cookies</a></li>
                    </ul>
                </div>
            </div>

            <div class="row">
                <div class="col-12 text-center border-top pt-3">
                    <small class="text-muted">Copyright © 2025 - Tous droits réservés</small>
                </div>
            </div>
        </div>
    </footer>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
<script>
console.log('Loading profit loss charts script...');

// Vérifier si Chart.js est chargé
if (typeof Chart === 'undefined') {
    console.error('Chart.js is not loaded!');
} else {
    console.log('Chart.js is loaded successfully');
}

// Afficher toutes les données disponibles
console.log('All available data:', {
    trend_dates: {{ trend_dates|tojson }},
    trend_data: {{ trend_data|tojson }},
    category_labels: {{ category_labels|tojson }},
    category_values: {{ category_values|tojson }},
    category_colors: {{ category_colors|tojson }}
});

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded');

    // Graphique de vue d'ensemble
    const overviewCanvas = document.getElementById('overviewChart');
    console.log('Overview canvas:', overviewCanvas);
    
    if (overviewCanvas) {
        try {
            const overviewChart = new Chart(overviewCanvas, {
                type: 'bar',
                data: {
                    labels: {{ trend_dates|tojson }},
                    datasets: [
                        {
                            label: 'Chiffre d\'affaires',
                            data: {{ trend_data.sales|tojson }},
                            backgroundColor: 'rgba(78, 115, 223, 0.8)',
                            borderColor: 'rgb(78, 115, 223)',
                            borderWidth: 1
                        },
                        {
                            label: 'Coûts des ventes',
                            data: {{ trend_data.costs|tojson }},
                            backgroundColor: 'rgba(231, 74, 59, 0.8)',
                            borderColor: 'rgb(231, 74, 59)',
                            borderWidth: 1
                        },
                        {
                            label: 'Charges',
                            data: {{ trend_data.charges|tojson }},
                            backgroundColor: 'rgba(246, 194, 62, 0.8)',
                            borderColor: 'rgb(246, 194, 62)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(2) + ' €';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });
            console.log('Overview chart created successfully');
        } catch (error) {
            console.error('Error creating overview chart:', error, error.stack);
        }
    }

    // Graphique d'évolution
    const resultsCanvas = document.getElementById('resultsChart');
    console.log('Results canvas:', resultsCanvas);
    
    if (resultsCanvas) {
        try {
            const resultsChart = new Chart(resultsCanvas, {
                type: 'line',
                data: {
                    labels: {{ trend_dates|tojson }},
                    datasets: [
                        {
                            label: 'Chiffre d\'affaires',
                            data: {{ trend_data.sales|tojson }},
                            borderColor: 'rgb(78, 115, 223)',
                            backgroundColor: 'rgba(78, 115, 223, 0.1)',
                            borderWidth: 2,
                            fill: true
                        },
                        {
                            label: 'Coûts des ventes',
                            data: {{ trend_data.costs|tojson }},
                            borderColor: 'rgb(231, 74, 59)',
                            backgroundColor: 'rgba(231, 74, 59, 0.1)',
                            borderWidth: 2,
                            fill: true
                        },
                        {
                            label: 'Charges',
                            data: {{ trend_data.charges|tojson }},
                            borderColor: 'rgb(246, 194, 62)',
                            backgroundColor: 'rgba(246, 194, 62, 0.1)',
                            borderWidth: 2,
                            fill: true
                        },
                        {
                            label: 'REX',
                            data: {{ trend_data.rex|tojson }},
                            borderColor: 'rgb(28, 200, 138)',
                            backgroundColor: 'rgba(28, 200, 138, 0.1)',
                            borderWidth: 2,
                            fill: true
                        },
                        {
                            label: 'RNET',
                            data: {{ trend_data.rnet|tojson }},
                            borderColor: 'rgb(54, 185, 204)',
                            backgroundColor: 'rgba(54, 185, 204, 0.1)',
                            borderWidth: 2,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(2) + ' €';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.raw.toFixed(2) + ' €';
                                }
                            }
                        }
                    }
                }
            });
            console.log('Results chart created successfully');
        } catch (error) {
            console.error('Error creating results chart:', error, error.stack);
        }
    }

    // Graphique des dépenses
    const expensesCanvas = document.getElementById('expensesChart');
    console.log('Expenses canvas:', expensesCanvas);
    
    if (expensesCanvas && {{ category_values|tojson }}.length > 0) {
        try {
            const expensesChart = new Chart(expensesCanvas, {
                type: 'doughnut',
                data: {
                    labels: {{ category_labels|tojson }},
                    datasets: [{
                        data: {{ category_values|tojson }},
                        backgroundColor: {{ category_colors|tojson }}
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.raw;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return `${context.label}: ${value.toFixed(2)} € (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
            console.log('Expenses chart created successfully');
        } catch (error) {
            console.error('Error creating expenses chart:', error, error.stack);
        }
    }

    // Graphique de progression
    const progressionCanvas = document.getElementById('progressionChart');
    console.log('Progression canvas:', progressionCanvas);
    
    if (progressionCanvas) {
        try {
            // Calculer les valeurs cumulatives pour chaque indicateur
            const sales = {{ trend_data.sales|tojson }};
            const costs = {{ trend_data.costs|tojson }};
            const charges = {{ trend_data.charges|tojson }};
            const rex = {{ trend_data.rex|tojson }};
            const rnet = {{ trend_data.rnet|tojson }};

            const cumulativeSales = [];
            const cumulativeCosts = [];
            const cumulativeCharges = [];
            const cumulativeRex = [];
            const cumulativeRnet = [];

            let totalSales = 0;
            let totalCosts = 0;
            let totalCharges = 0;
            let totalRex = 0;
            let totalRnet = 0;

            for (let i = 0; i < sales.length; i++) {
                totalSales += sales[i];
                totalCosts += costs[i];
                totalCharges += charges[i];
                totalRex += rex[i];
                totalRnet += rnet[i];

                cumulativeSales.push(totalSales);
                cumulativeCosts.push(totalCosts);
                cumulativeCharges.push(totalCharges);
                cumulativeRex.push(totalRex);
                cumulativeRnet.push(totalRnet);
            }

            const progressionChart = new Chart(progressionCanvas, {
                type: 'line',
                data: {
                    labels: {{ trend_dates|tojson }},
                    datasets: [
                        {
                            label: 'Chiffre d\'affaires cumulé',
                            data: cumulativeSales,
                            borderColor: 'rgb(78, 115, 223)',
                            backgroundColor: 'rgba(78, 115, 223, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: false
                        },
                        {
                            label: 'Coûts des ventes cumulés',
                            data: cumulativeCosts,
                            borderColor: 'rgb(231, 74, 59)',
                            backgroundColor: 'rgba(231, 74, 59, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: false
                        },
                        {
                            label: 'Charges cumulées',
                            data: cumulativeCharges,
                            borderColor: 'rgb(246, 194, 62)',
                            backgroundColor: 'rgba(246, 194, 62, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: false
                        },
                        {
                            label: 'REX cumulé',
                            data: cumulativeRex,
                            borderColor: 'rgb(28, 200, 138)',
                            backgroundColor: 'rgba(28, 200, 138, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: false
                        },
                        {
                            label: 'RNET cumulé',
                            data: cumulativeRnet,
                            borderColor: 'rgb(54, 185, 204)',
                            backgroundColor: 'rgba(54, 185, 204, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(2) + ' €';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.raw.toFixed(2) + ' €';
                                }
                            }
                        }
                    }
                }
            });
            console.log('Progression chart created successfully');
        } catch (error) {
            console.error('Error creating progression chart:', error, error.stack);
        }
    }
});
</script>
{% endblock %}
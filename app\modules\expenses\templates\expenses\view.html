{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div>
            <a href="{{ url_for('expenses.edit', id=expense.id) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Modifier
            </a>
            <a href="{{ url_for('expenses.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour
            </a>
        </div>
    </div>

    <!-- Expense Details -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informations de la dépense</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th style="width: 150px;">Date :</th>
                                    <td>{{ expense.date | datetime_auto('date') }}</td>
                                </tr>
                                <tr>
                                    <th>Catégorie :</th>
                                    <td>
                                        {% if expense.category %}
                                            <span class="badge" style="background-color: {{ expense.category.color if expense.category.color else '#6c757d' }}">
                                                {{ expense.category.name }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">Sans catégorie</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Montant :</th>
                                    <td>
                                        <span class="h5 text-danger">{{ expense.amount|format_currency }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Mode de paiement :</th>
                                    <td>
                                        {% if expense.payment_method == 'cash' %}
                                            <i class="fas fa-money-bill-wave text-success"></i> Espèces
                                        {% elif expense.payment_method == 'card' %}
                                            <i class="fas fa-credit-card text-primary"></i> Carte
                                        {% elif expense.payment_method == 'check' %}
                                            <i class="fas fa-money-check text-info"></i> Chèque
                                        {% elif expense.payment_method == 'transfer' %}
                                            <i class="fas fa-exchange-alt text-warning"></i> Virement
                                        {% else %}
                                            <i class="fas fa-question-circle text-muted"></i> {{ expense.payment_method or 'Non spécifié' }}
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if expense.reference %}
                                <tr>
                                    <th>Référence :</th>
                                    <td>{{ expense.reference }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th style="width: 150px;">Récurrente :</th>
                                    <td>
                                        {% if expense.is_recurring %}
                                            <span class="badge bg-info">
                                                <i class="fas fa-redo"></i> 
                                                Oui ({{ expense.recurring_interval }})
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">Non</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Créée le :</th>
                                    <td>{{ expense.created_at | datetime_auto('full') }}</td>
                                </tr>
                                {% if expense.updated_at %}
                                <tr>
                                    <th>Modifiée le :</th>
                                    <td>{{ expense.updated_at | datetime_auto('full') }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>
                    
                    {% if expense.description %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="font-weight-bold">Description :</h6>
                            <p class="text-muted">{{ expense.description }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            {% if expense.image_path %}
            <!-- Image Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Justificatif</h6>
                </div>
                <div class="card-body text-center">
                    <img src="{{ url_for('static', filename=expense.image_path) }}" 
                         class="img-fluid rounded" 
                         alt="Justificatif de dépense"
                         style="max-height: 300px;">
                </div>
            </div>
            {% endif %}
            
            <!-- Actions Card -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('expenses.edit', id=expense.id) }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Modifier cette dépense
                        </a>
                        <button type="button" class="btn btn-danger" onclick="confirmDelete({{ expense.id }})">
                            <i class="fas fa-trash"></i> Supprimer cette dépense
                        </button>
                        <a href="{{ url_for('expenses.create') }}" class="btn btn-success">
                            <i class="fas fa-plus"></i> Nouvelle dépense
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Êtes-vous sûr de vouloir supprimer cette dépense ? Cette action est irréversible.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(expenseId) {
    const form = document.getElementById('deleteForm');
    form.action = `/expenses/delete/${expenseId}`;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
{% endblock %}

{% extends "employees/base_hr.html" %}

{% block title %}Actions en Lot{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-tasks me-2"></i>Actions en Lot
    </h1>
    <a href="{{ url_for('employees.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>Retour
    </a>
</div>

<!-- Sélection des employés -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-users me-2"></i>Sélectionner les Employés
        </h5>
    </div>
    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-6">
                <button type="button" class="btn btn-outline-primary" onclick="selectAll()">
                    <i class="fas fa-check-square me-1"></i>Tout sélectionner
                </button>
                <button type="button" class="btn btn-outline-secondary ms-2" onclick="selectNone()">
                    <i class="fas fa-square me-1"></i>Tout désélectionner
                </button>
            </div>
            <div class="col-md-6 text-end">
                <span id="selectedCount" class="badge bg-info">0 employé(s) sélectionné(s)</span>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th width="50">
                            <input type="checkbox" id="selectAllCheckbox" onchange="toggleAll()">
                        </th>
                        <th>Employé</th>
                        <th>Poste</th>
                        <th>Département</th>
                        <th>Statut</th>
                        <th>Date d'embauche</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in active_employees %}
                    <tr>
                        <td>
                            <input type="checkbox" class="employee-checkbox" value="{{ employee.id }}" onchange="updateSelectedCount()">
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                    {{ employee.first_name[0] }}{{ employee.last_name[0] }}
                                </div>
                                <div>
                                    <strong>{{ employee.full_name }}</strong><br>
                                    <small class="text-muted">{{ employee.employee_id }}</small>
                                </div>
                            </div>
                        </td>
                        <td>{{ employee.position }}</td>
                        <td>{{ employee.department or '-' }}</td>
                        <td>
                            <span class="badge bg-success">{{ employee.status.replace('_', ' ').title() }}</span>
                        </td>
                        <td>{{ employee.hire_date | datetime_auto('date') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Actions disponibles -->
<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>Modifications en Lot
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary" onclick="bulkUpdateDepartment()">
                        <i class="fas fa-building me-2"></i>Changer Département
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="bulkUpdateSalary()">
                        <i class="fas fa-money-bill-wave me-2"></i>Ajuster Salaires
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="bulkUpdateStatus()">
                        <i class="fas fa-user-tag me-2"></i>Modifier Statut
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar me-2"></i>Plannings en Lot
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary" onclick="bulkCreateSchedule()">
                        <i class="fas fa-calendar-plus me-2"></i>Créer Planning
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="bulkUpdateSchedule()">
                        <i class="fas fa-calendar-edit me-2"></i>Modifier Planning
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="bulkDeleteSchedule()">
                        <i class="fas fa-calendar-times me-2"></i>Supprimer Planning
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-export me-2"></i>Export et Rapports
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-success" onclick="exportSelectedEmployees()">
                        <i class="fas fa-download me-2"></i>Exporter Sélection
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="generateBulkReport()">
                        <i class="fas fa-chart-bar me-2"></i>Rapport Groupé
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="printEmployeeCards()">
                        <i class="fas fa-print me-2"></i>Imprimer Fiches
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Actions avancées -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-cogs me-2"></i>Actions Avancées
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <button type="button" class="btn btn-outline-primary w-100" onclick="bulkCreateAttendance()">
                    <i class="fas fa-clock me-2"></i>Créer Présences
                </button>
            </div>
            <div class="col-md-3">
                <button type="button" class="btn btn-outline-success w-100" onclick="bulkGeneratePayroll()">
                    <i class="fas fa-money-bill me-2"></i>Générer Paies
                </button>
            </div>
            <div class="col-md-3">
                <button type="button" class="btn btn-outline-info w-100" onclick="bulkScheduleEvaluation()">
                    <i class="fas fa-star me-2"></i>Programmer Évaluations
                </button>
            </div>
            <div class="col-md-3">
                <button type="button" class="btn btn-outline-warning w-100" onclick="bulkSendNotification()">
                    <i class="fas fa-bell me-2"></i>Envoyer Notifications
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
    font-weight: bold;
}
</style>

<script>
function selectAll() {
    const checkboxes = document.querySelectorAll('.employee-checkbox');
    checkboxes.forEach(checkbox => checkbox.checked = true);
    document.getElementById('selectAllCheckbox').checked = true;
    updateSelectedCount();
}

function selectNone() {
    const checkboxes = document.querySelectorAll('.employee-checkbox');
    checkboxes.forEach(checkbox => checkbox.checked = false);
    document.getElementById('selectAllCheckbox').checked = false;
    updateSelectedCount();
}

function toggleAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.employee-checkbox');
    checkboxes.forEach(checkbox => checkbox.checked = selectAllCheckbox.checked);
    updateSelectedCount();
}

function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.employee-checkbox:checked');
    const count = selectedCheckboxes.length;
    document.getElementById('selectedCount').textContent = `${count} employé(s) sélectionné(s)`;
    
    // Mettre à jour le checkbox "Tout sélectionner"
    const allCheckboxes = document.querySelectorAll('.employee-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    selectAllCheckbox.checked = count === allCheckboxes.length;
    selectAllCheckbox.indeterminate = count > 0 && count < allCheckboxes.length;
}

function getSelectedEmployeeIds() {
    const selectedCheckboxes = document.querySelectorAll('.employee-checkbox:checked');
    return Array.from(selectedCheckboxes).map(checkbox => checkbox.value);
}

function validateSelection(action) {
    const selectedIds = getSelectedEmployeeIds();
    if (selectedIds.length === 0) {
        alert(`Veuillez sélectionner au moins un employé pour ${action}.`);
        return false;
    }
    return selectedIds;
}

// Actions de modification en lot
function bulkUpdateDepartment() {
    const selectedIds = validateSelection('changer le département');
    if (!selectedIds) return;
    
    const newDepartment = prompt('Nouveau département :');
    if (newDepartment !== null) {
        // Ici, vous pouvez implémenter l'appel API
        alert(`Département "${newDepartment}" assigné à ${selectedIds.length} employé(s).`);
    }
}

function bulkUpdateSalary() {
    const selectedIds = validateSelection('ajuster les salaires');
    if (!selectedIds) return;
    
    const adjustment = prompt('Ajustement salarial (% ou montant fixe) :');
    if (adjustment !== null) {
        alert(`Ajustement salarial de "${adjustment}" appliqué à ${selectedIds.length} employé(s).`);
    }
}

function bulkUpdateStatus() {
    const selectedIds = validateSelection('modifier le statut');
    if (!selectedIds) return;
    
    const newStatus = prompt('Nouveau statut (ACTIVE, INACTIVE, SUSPENDED) :');
    if (newStatus !== null) {
        alert(`Statut "${newStatus}" assigné à ${selectedIds.length} employé(s).`);
    }
}

// Actions de planning en lot
function bulkCreateSchedule() {
    const selectedIds = validateSelection('créer un planning');
    if (!selectedIds) return;
    
    alert(`Création de planning pour ${selectedIds.length} employé(s). Fonctionnalité à implémenter.`);
}

function bulkUpdateSchedule() {
    const selectedIds = validateSelection('modifier le planning');
    if (!selectedIds) return;
    
    alert(`Modification de planning pour ${selectedIds.length} employé(s). Fonctionnalité à implémenter.`);
}

function bulkDeleteSchedule() {
    const selectedIds = validateSelection('supprimer le planning');
    if (!selectedIds) return;
    
    if (confirm(`Supprimer le planning de ${selectedIds.length} employé(s) ?`)) {
        alert('Suppression de planning. Fonctionnalité à implémenter.');
    }
}

// Actions d'export et rapports
function exportSelectedEmployees() {
    const selectedIds = validateSelection('exporter');
    if (!selectedIds) return;
    
    const format = prompt('Format d\'export (excel, csv, pdf) :') || 'excel';
    alert(`Export de ${selectedIds.length} employé(s) en format ${format}. Fonctionnalité à implémenter.`);
}

function generateBulkReport() {
    const selectedIds = validateSelection('générer un rapport');
    if (!selectedIds) return;
    
    alert(`Génération de rapport pour ${selectedIds.length} employé(s). Fonctionnalité à implémenter.`);
}

function printEmployeeCards() {
    const selectedIds = validateSelection('imprimer les fiches');
    if (!selectedIds) return;
    
    alert(`Impression des fiches de ${selectedIds.length} employé(s). Fonctionnalité à implémenter.`);
}

// Actions avancées
function bulkCreateAttendance() {
    const selectedIds = validateSelection('créer des présences');
    if (!selectedIds) return;
    
    alert(`Création de présences pour ${selectedIds.length} employé(s). Fonctionnalité à implémenter.`);
}

function bulkGeneratePayroll() {
    const selectedIds = validateSelection('générer la paie');
    if (!selectedIds) return;
    
    if (confirm(`Générer la paie pour ${selectedIds.length} employé(s) ?`)) {
        alert('Génération de paie en lot. Fonctionnalité à implémenter.');
    }
}

function bulkScheduleEvaluation() {
    const selectedIds = validateSelection('programmer des évaluations');
    if (!selectedIds) return;
    
    alert(`Programmation d'évaluations pour ${selectedIds.length} employé(s). Fonctionnalité à implémenter.`);
}

function bulkSendNotification() {
    const selectedIds = validateSelection('envoyer des notifications');
    if (!selectedIds) return;
    
    const message = prompt('Message à envoyer :');
    if (message !== null) {
        alert(`Notification envoyée à ${selectedIds.length} employé(s). Fonctionnalité à implémenter.`);
    }
}

// Initialiser le compteur au chargement
document.addEventListener('DOMContentLoaded', function() {
    updateSelectedCount();
});
</script>
{% endblock %}

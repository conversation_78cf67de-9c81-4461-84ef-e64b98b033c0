<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Rapport des ventes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            font-size: 12px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .info {
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f5f5f5;
        }
        .text-end {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .totals {
            margin-top: 20px;
            float: right;
            width: 300px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            font-size: 10px;
            color: #666;
        }
        @page {
            margin: 40px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Rapport des ventes</h1>
        <p>Période : {{ start_date | datetime_auto('date') }} - {{ end_date | datetime_auto('date') }}</p>
    </div>

    <div class="info">
        <p><strong>Date d'édition :</strong> {{ datetime.utcnow().strftime('%d/%m/%Y %H:%M') }}</p>
        <p><strong>Nombre de ventes :</strong> {{ sales|length }}</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>Date</th>
                <th>Référence</th>
                <th>Statut</th>
                <th class="text-end">Total HT</th>
                {% if include_taxes %}
                <th class="text-end">TVA</th>
                {% endif %}
                <th class="text-end">Total TTC</th>
                {% if include_items %}
                <th>Produits</th>
                {% endif %}
            </tr>
        </thead>
        <tbody>
            {% for sale in sales %}
            <tr>
                <td>{{ sale.created_at | datetime_auto('full') }}             <td>{{ sale.reference }}</td>
                <td>
                    {% if sale.status.value == 'PAID' %}
                    Terminée
                    {% elif sale.status.value == 'PENDING' %}
                    En cours
                    {% elif sale.status.value == 'CANCELLED' %}
                    Annulée
                    {% endif %}
                </td>
                <td class="text-end">{{ "%.2f"|format(sale.total_ht) }} €</td>
                {% if include_taxes %}
                <td class="text-end">{{ "%.2f"|format(sale.total_tax) }} €</td>
                {% endif %}
                <td class="text-end">{{ "%.2f"|format(sale.total_ttc) }} €</td>
                {% if include_items %}
                <td>
                    {% for item in sale.items.all() %}
                    {{ item.quantity }}x {{ item.product.name }}{% if not loop.last %}, {% endif %}
                    {% endfor %}
                </td>
                {% endif %}
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <div class="totals">
        <table>
            <tr>
                <th>Total HT</th>
                <td class="text-end">{{ "%.2f"|format(sales|sum(attribute='total_ht')) }} €</td>
            </tr>
            {% if include_taxes %}
            <tr>
                <th>Total TVA</th>
                <td class="text-end">{{ "%.2f"|format(sales|sum(attribute='total_tax')) }} €</td>
            </tr>
            {% endif %}
            <tr>
                <th>Total TTC</th>
                <td class="text-end">{{ "%.2f"|format(sales|sum(attribute='total_ttc')) }} €</td>
            </tr>
        </table>
    </div>

    <div style="clear: both;"></div>

    <div class="footer">
        <p>{{ config.get('SHOP_NAME', 'Mon magasin') }}</p>
        <p>Document généré le {{ datetime.utcnow().strftime('%d/%m/%Y à %H:%M') }}</p>
    </div>
</body>
</html> 
{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-tachometer-alt"></i> Tableau de Bord - Tables & Réservations</h1>
        <div>
            <a href="{{ url_for('tables.index') }}" class="btn btn-outline-primary me-2">
                <i class="fas fa-chair"></i> Tables
            </a>
            <a href="{{ url_for('tables.reservations') }}" class="btn btn-info">
                <i class="fas fa-calendar-alt"></i> Réservations
            </a>
        </div>
    </div>

    <!-- Statistiques des tables -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ available_tables }}</h4>
                            <p class="mb-0">Tables Disponibles</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ reserved_tables }}</h4>
                            <p class="mb-0">Tables Réservées</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ occupied_tables }}</h4>
                            <p class="mb-0">Tables Occupées</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_tables }}</h4>
                            <p class="mb-0">Total Tables</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chair fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Réservations actives -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-clock text-success"></i> Réservations En Cours</h5>
                </div>
                <div class="card-body">
                    {% if active_reservations %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Table</th>
                                        <th>Client</th>
                                        <th>Heure</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for reservation in active_reservations %}
                                    <tr>
                                        <td>{{ reservation.table.number }}</td>
                                        <td>{{ reservation.customer_name }}</td>
                                        <td>{{ reservation.reservation_date | datetime_auto('time') }} }}</td>
                                        <td>
                                            <a href="{{ url_for('tables.reservation_details', id=reservation.id) }}" 
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted mb-0">Aucune réservation en cours</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Prochaines réservations -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bell text-warning"></i> Prochaines Réservations (2h)</h5>
                </div>
                <div class="card-body">
                    {% if upcoming_reservations %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Table</th>
                                        <th>Client</th>
                                        <th>Heure</th>
                                        <th>Dans</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for reservation in upcoming_reservations %}
                                    <tr>
                                        <td>{{ reservation.table.number }}</td>
                                        <td>{{ reservation.customer_name }}</td>
                                        <td>{{ reservation.reservation_date | datetime_auto('time') }} }}</td>
                                        <td>
                                            {% set time_diff = (reservation.reservation_date - now).total_seconds() / 60 %}
                                            {% if time_diff < 60 %}
                                                <span class="badge bg-danger">{{ time_diff|int }} min</span>
                                            {% else %}
                                                <span class="badge bg-warning">{{ (time_diff/60)|int }}h {{ (time_diff%60)|int }}min</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('tables.reservation_details', id=reservation.id) }}" 
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted mb-0">Aucune réservation dans les 2 prochaines heures</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Résumé du jour -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-calendar-day"></i> Résumé du Jour</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3 class="text-primary">{{ today_reservations|length }}</h3>
                                <p class="mb-0">Réservations Totales</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3 class="text-success">{{ active_reservations|length }}</h3>
                                <p class="mb-0">En Cours</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3 class="text-warning">{{ upcoming_reservations|length }}</h3>
                                <p class="mb-0">À Venir (2h)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-refresh toutes les 5 minutes
setTimeout(function() {
    location.reload();
}, 300000);

// Notification pour les réservations imminentes
{% if upcoming_reservations %}
    {% for reservation in upcoming_reservations %}
        {% set time_diff = (reservation.reservation_date - now).total_seconds() / 60 %}
        {% if time_diff <= 15 %}
            // Notification pour réservation dans moins de 15 minutes
            console.log('Réservation imminente: Table {{ reservation.table.number }} - {{ reservation.customer_name }}');
        {% endif %}
    {% endfor %}
{% endif %}
</script>
{% endblock %}

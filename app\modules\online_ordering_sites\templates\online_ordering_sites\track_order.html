{% extends "online_ordering_sites/base.html" %}

{% block title %}Suivi de commande - {{ site.site_name or site.owner.username }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- En-tête -->
            <div class="text-center mb-4">
                <h2 class="h3 text-primary">
                    <i class="fas fa-search-location me-2"></i>
                    Suivi de votre commande
                </h2>
                <p class="text-muted">Commande #{{ order.order_number }}</p>
            </div>

            <!-- Informations de la commande -->
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Informations de la commande
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Numéro :</strong> #{{ order.order_number }}</p>
                            <p><strong>Date :</strong> {{ order.ordered_at | datetime_auto('full') }}                            <p><strong>Type :</strong> 
                                {% if order.order_type.value == 'delivery' %}
                                    <span class="badge bg-info">Livraison</span>
                                {% elif order.order_type.value == 'pickup' %}
                                    <span class="badge bg-warning">À emporter</span>
                                {% elif order.order_type.value == 'dine_in' %}
                                    <span class="badge bg-primary">Sur place</span>
                                {% elif order.order_type.value == 'drive_through' %}
                                    <span class="badge bg-success">Drive</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Total :</strong> {{ "%.2f"|format(order.total_amount) }}€</p>
                            <p><strong>Paiement :</strong> 
                                {% if order.payment_method.value == 'cash_on_delivery' %}
                                    <span class="badge bg-warning">À la livraison</span>
                                {% else %}
                                    <span class="badge bg-success">En ligne</span>
                                {% endif %}
                            </p>
                            <p><strong>Statut :</strong> <span id="current-status" class="badge bg-info">{{ order.status.value }}</span></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Suivi en temps réel -->
            <div class="card shadow mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-route me-2"></i>
                        Suivi en temps réel
                    </h5>
                </div>
                <div class="card-body">
                    <div id="order-timeline">
                        <!-- Le timeline sera mis à jour via JavaScript -->
                    </div>
                    
                    <div class="text-center mt-4">
                        <p id="estimated-time" class="text-muted"></p>
                        <button class="btn btn-outline-primary" onclick="refreshStatus()">
                            <i class="fas fa-sync-alt"></i> Actualiser
                        </button>
                    </div>
                </div>
            </div>

            <!-- Articles de la commande -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-bag me-2"></i>
                        Vos articles
                    </h5>
                </div>
                <div class="card-body">
                    {% for item in order.items %}
                    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                        <div>
                            <h6 class="mb-1">{{ item.product.name }}</h6>
                            <small class="text-muted">{{ "%.2f"|format(item.unit_price) }}€ l'unité</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-secondary">x{{ item.quantity }}</span>
                            <div><strong>{{ "%.2f"|format(item.total_price) }}€</strong></div>
                        </div>
                    </div>
                    {% endfor %}
                    
                    <div class="mt-3 pt-3 border-top">
                        <div class="d-flex justify-content-between">
                            <span>Sous-total :</span>
                            <span>{{ "%.2f"|format(order.subtotal) }}€</span>
                        </div>
                        {% if order.delivery_fee > 0 %}
                        <div class="d-flex justify-content-between">
                            <span>Frais de livraison :</span>
                            <span>{{ "%.2f"|format(order.delivery_fee) }}€</span>
                        </div>
                        {% endif %}
                        <div class="d-flex justify-content-between fw-bold">
                            <span>Total :</span>
                            <span>{{ "%.2f"|format(order.total_amount) }}€</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Adresse de livraison (si applicable) -->
            {% if order.order_type.value == 'delivery' and order.delivery_address %}
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Adresse de livraison
                    </h5>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ order.delivery_address }}</p>
                    {% if order.customer_notes %}
                    <hr>
                    <p class="mb-0"><strong>Instructions :</strong> {{ order.customer_notes }}</p>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Actions -->
            <div class="text-center">
                <a href="{{ url_for('online_ordering_sites.menu') }}" class="btn btn-primary">
                    <i class="fas fa-utensils me-2"></i>
                    Commander à nouveau
                </a>
                <a href="{{ url_for('online_ordering_sites.index') }}" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-home me-2"></i>
                    Retour à l'accueil
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
const orderNumber = '{{ order.order_number }}';

function refreshStatus() {
    fetch(`/api/track/${orderNumber}`)
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            updateOrderStatus(data.order);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function updateOrderStatus(order) {
    // Mettre à jour le statut actuel
    const statusElement = document.getElementById('current-status');
    statusElement.textContent = order.status_display;
    statusElement.className = 'badge ' + getStatusBadgeClass(order.status);
    
    // Mettre à jour le temps estimé
    const estimatedTimeElement = document.getElementById('estimated-time');
    if (order.estimated_time) {
        estimatedTimeElement.textContent = `Temps estimé : ${order.estimated_time}`;
    } else {
        estimatedTimeElement.textContent = '';
    }
    
    // Mettre à jour la timeline
    updateTimeline(order);
}

function getStatusBadgeClass(status) {
    const statusClasses = {
        'pending': 'bg-warning',
        'confirmed': 'bg-info',
        'preparing': 'bg-primary',
        'ready': 'bg-success',
        'out_for_delivery': 'bg-info',
        'delivered': 'bg-dark',
        'cancelled': 'bg-danger'
    };
    return statusClasses[status] || 'bg-secondary';
}

function updateTimeline(order) {
    const timeline = document.getElementById('order-timeline');
    
    const steps = [
        { key: 'ordered', label: 'Commande passée', time: order.ordered_at, icon: 'fas fa-shopping-cart' },
        { key: 'confirmed', label: 'Commande confirmée', time: order.confirmed_at, icon: 'fas fa-check-circle' },
        { key: 'preparing', label: 'En préparation', time: order.prepared_at, icon: 'fas fa-utensils' },
        { key: 'ready', label: 'Prête', time: order.ready_at, icon: 'fas fa-bell' }
    ];
    
    // Ajouter les étapes spécifiques selon le type de commande
    if (order.order_type === 'delivery') {
        steps.push(
            { key: 'out_for_delivery', label: 'En livraison', time: order.out_for_delivery_at, icon: 'fas fa-truck' },
            { key: 'delivered', label: 'Livrée', time: order.delivered_at, icon: 'fas fa-home' }
        );
    } else {
        steps.push(
            { key: 'delivered', label: 'Récupérée', time: order.delivered_at, icon: 'fas fa-check' }
        );
    }
    
    let timelineHTML = '<div class="timeline">';
    
    steps.forEach((step, index) => {
        const isCompleted = step.time !== null;
        const isCurrent = getCurrentStep(order.status) === index;
        
        timelineHTML += `
            <div class="timeline-item ${isCompleted ? 'completed' : ''} ${isCurrent ? 'current' : ''}">
                <div class="timeline-marker">
                    <i class="${step.icon}"></i>
                </div>
                <div class="timeline-content">
                    <h6>${step.label}</h6>
                    ${step.time ? `<small class="text-muted">${step.time}</small>` : ''}
                </div>
            </div>
        `;
    });
    
    timelineHTML += '</div>';
    timeline.innerHTML = timelineHTML;
}

function getCurrentStep(status) {
    const statusSteps = {
        'pending': 0,
        'confirmed': 1,
        'preparing': 2,
        'ready': 3,
        'out_for_delivery': 4,
        'delivered': 5
    };
    return statusSteps[status] || 0;
}

// Actualiser automatiquement toutes les 30 secondes
setInterval(refreshStatus, 30000);

// Charger le statut initial
document.addEventListener('DOMContentLoaded', function() {
    refreshStatus();
});
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    padding-bottom: 20px;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -19px;
    top: 30px;
    width: 2px;
    height: calc(100% - 10px);
    background-color: #dee2e6;
}

.timeline-item.completed:not(:last-child)::before {
    background-color: #28a745;
}

.timeline-marker {
    position: absolute;
    left: -30px;
    top: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #6c757d;
}

.timeline-item.completed .timeline-marker {
    background-color: #28a745;
    color: white;
}

.timeline-item.current .timeline-marker {
    background-color: #007bff;
    color: white;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-weight: 600;
}
</style>
{% endblock %}

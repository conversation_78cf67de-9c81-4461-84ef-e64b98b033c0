{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#filterModal">
                <i class="fas fa-filter"></i> Filtrer
            </button>
            <button type="button" class="btn btn-success" onclick="exportSalesReport()">
                <i class="fas fa-file-export"></i> Exporter
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Chiffre d'affaires</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_revenue|format_currency }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-euro-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Nombre de ventes</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_sales }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Panier moyen</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ average_sale|format_currency }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calculator fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Moyenne journalière</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ daily_average|format_currency }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <!-- Sales Trend Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Évolution des ventes</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area" style="height: 300px;">
                        <canvas id="salesTrendChart"></canvas>
                    </div>
                </div>
            </div>
            <!-- Tableaux POS et Online -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Ventes POS</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="salesTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Numéro</th>
                                    <th>Produits</th>
                                    <th>Quantité</th>
                                    <th>Total</th>
                                    <th>Mode de paiement</th>
                                    <th>Statut paiement</th>
                                    <th>Type</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sale in pos_sales %}
                                <tr>
                                    <td data-order="{{ sale.created_at | datetime_auto('full') }} sale.created_at | datetime_auto('full') }}</td>
                                    <td>#{{ sale.id }}</td>
                                    <td>
                                        {% for item in sale.items %}
                                        {{ item.product.name }} (x{{ item.quantity }}){% if not loop.last %}, {% endif %}
                                        {% endfor %}
                                    </td>
                                    <td>{{ sale.items|sum(attribute='quantity') }}</td>
                                    <td>{{ sale.total|format_currency }}</td>
                                    <td>
                                        {% set pm = sale.payment_method.value if sale.payment_method and sale.payment_method.value else sale.payment_method %}
                                        {% set payments_list = sale.payments.all() %}
                                        {% if not pm and payments_list and payments_list|length > 0 %}
                                            {% set pm = payments_list[0].method.value if payments_list[0].method.value else payments_list[0].method %}
                                        {% endif %}
                                        {% if pm and pm in payment_method_colors %}
                                            {% set badge_color = payment_method_colors[pm] %}
                                            {% if pm == 'cash' %}
                                                <span class="badge bg-{{ badge_color }}" data-bs-toggle="tooltip" title="Espèces"><i class="fas fa-money-bill-wave"></i> Espèces</span>
                                            {% elif pm == 'card' %}
                                                <span class="badge bg-{{ badge_color }}" data-bs-toggle="tooltip" title="Carte bancaire"><i class="fas fa-credit-card"></i> Carte</span>
                                            {% elif pm == 'check' %}
                                                <span class="badge bg-{{ badge_color }}" data-bs-toggle="tooltip" title="Chèque"><i class="fas fa-money-check"></i> Chèque</span>
                                            {% elif pm == 'transfer' %}
                                                <span class="badge bg-{{ badge_color }} text-dark" data-bs-toggle="tooltip" title="Virement"><i class="fas fa-exchange-alt"></i> Virement</span>
                                            {% elif pm == 'online_payment' %}
                                                <span class="badge bg-{{ badge_color }}" data-bs-toggle="tooltip" title="Paiement en ligne"><i class="fas fa-globe"></i> Paiement en ligne</span>
                                            {% elif pm == 'cash_on_delivery' %}
                                                <span class="badge bg-{{ badge_color }}" data-bs-toggle="tooltip" title="Espèces à la livraison"><i class="fas fa-money-bill-wave"></i> Espèces (livraison)</span>
                                            {% elif pm == 'card_on_delivery' %}
                                                <span class="badge bg-{{ badge_color }}" data-bs-toggle="tooltip" title="Carte à la livraison"><i class="fas fa-credit-card"></i> Carte (livraison)</span>
                                            {% else %}
                                                <span class="badge bg-{{ badge_color }}" data-bs-toggle="tooltip" title="{{ pm|capitalize }}"><i class="fas fa-ellipsis-h"></i> {{ pm|capitalize }}</span>
                                            {% endif %}
                                        {% elif pm %}
                                            <span class="badge bg-warning text-dark" data-bs-toggle="tooltip" title="Autre"><i class="fas fa-ellipsis-h"></i> {{ pm|capitalize }}</span>
                                        {% else %}
                                            <span class="badge bg-warning text-dark" data-bs-toggle="tooltip">Non renseigné</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if sale.status.value in ['PAID', 'COMPLETED'] %}
                                            <span class="badge bg-success" data-bs-toggle="tooltip" title="Payé">Payé</span>
                                        {% elif sale.status.value == 'PENDING' %}
                                            <span class="badge bg-warning text-dark" data-bs-toggle="tooltip" title="En attente">En attente</span>
                                        {% elif sale.status.value == 'CANCELLED' %}
                                            <span class="badge bg-danger" data-bs-toggle="tooltip" title="Annulé">Annulé</span>
                                        {% else %}
                                            <span class="badge bg-primary" data-bs-toggle="tooltip" title="{{ sale.status.value|capitalize }}">{{ sale.status.value|capitalize }}</span>
                                        {% endif %}
                                    </td>
                                    <td><span class="badge bg-info" data-bs-toggle="tooltip" title="POS"><i class="fas fa-desktop"></i> POS</span></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-info" onclick="viewSale({{ sale.id }})" data-bs-toggle="tooltip" title="Voir la vente">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="window.open('/pos/sales/{{ sale.id }}/print', '_blank')" data-bs-toggle="tooltip" title="Imprimer le reçu POS">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="card shadow mb-4 mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Ventes en ligne</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="onlineSalesTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Numéro</th>
                                    <th>Produits</th>
                                    <th>Quantité</th>
                                    <th>Total</th>
                                    <th>Mode de paiement</th>
                                    <th>Statut paiement</th>
                                    <th>Type</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in online_orders %}
                                <tr>
                                    <td data-order="{{ order.ordered_at | datetime_auto('full') }} order.ordered_at | datetime_auto('full') }}</td>
                                    <td>#{{ order.id }}</td>
                                    <td>
                                        {% for item in order.items %}
                                        {{ item.product.name }} (x{{ item.quantity }}){% if not loop.last %}, {% endif %}
                                        {% endfor %}
                                    </td>
                                    <td>{{ order.items|sum(attribute='quantity') }}</td>
                                    <td>{{ order.total_amount|format_currency }}</td>
                                    <td>
                                        {% if order.payment_method %}
                                            {% set pm = order.payment_method.value if order.payment_method.value else order.payment_method %}
                                            {% if pm and pm in payment_method_colors %}
                                                {% set badge_color = payment_method_colors[pm] %}
                                                {% if pm == 'cash_on_delivery' %}
                                                    <span class="badge bg-{{ badge_color }}" data-bs-toggle="tooltip" title="Espèces à la livraison"><i class="fas fa-money-bill-wave"></i> Espèces (livraison)</span>
                                                {% elif pm == 'card_on_delivery' %}
                                                    <span class="badge bg-{{ badge_color }}" data-bs-toggle="tooltip" title="Carte à la livraison"><i class="fas fa-credit-card"></i> Carte (livraison)</span>
                                                {% elif pm == 'online_payment' or pm == 'card' %}
                                                    <span class="badge" style="background-color:#6f42c1;color:#fff;" data-bs-toggle="tooltip" title="Paiement en ligne"><i class="fas fa-globe"></i> Paiement en ligne</span>
                                                {% else %}
                                                    <span class="badge bg-{{ badge_color }}" data-bs-toggle="tooltip" title="{{ pm|capitalize }}"><i class="fas fa-ellipsis-h"></i> {{ pm|capitalize }}</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="badge bg-warning text-dark" data-bs-toggle="tooltip" title="Autre"><i class="fas fa-ellipsis-h"></i> {{ pm|capitalize }}</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge bg-warning text-dark" data-bs-toggle="tooltip">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if order.payment_status.value == 'PAID' %}
                                            <span class="badge bg-success" data-bs-toggle="tooltip" title="Payé">Payé</span>
                                        {% elif order.payment_status.value == 'PENDING' %}
                                            <span class="badge bg-warning text-dark" data-bs-toggle="tooltip" title="En attente">En attente</span>
                                        {% elif order.payment_status.value == 'CANCELLED' %}
                                            <span class="badge bg-danger" data-bs-toggle="tooltip" title="Annulé">Annulé</span>
                                        {% else %}
                                            <span class="badge bg-primary" data-bs-toggle="tooltip" title="{{ order.payment_status.value|capitalize }}">{{ order.payment_status.value|capitalize }}</span>
                                        {% endif %}
                                    </td>
                                    <td><span class="badge bg-warning text-dark" data-bs-toggle="tooltip" title="En ligne"><i class="fas fa-globe"></i> En ligne</span></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-info" onclick="viewSale({{ order.id }})" data-bs-toggle="tooltip" title="Voir la commande en ligne">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="window.open('/admin/orders/{{ order.id }}/print', '_blank')" data-bs-toggle="tooltip" title="Imprimer le reçu en ligne">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- Top Products Chart + Répartition POS/Online -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top 5 des produits</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie">
                        <canvas id="topProductsChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Répartition ventes POS vs Online</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie">
                        <canvas id="salesSourceChart"></canvas>
                    </div>
                    <div class="mt-3 text-center small">
                        <span class="badge bg-info"><i class="fas fa-desktop"></i> POS</span>
                        <span class="badge bg-warning text-dark ms-2"><i class="fas fa-globe"></i> Online</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filter Modal -->
<div class="modal fade" id="filterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Filtrer le rapport</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="filterForm" method="GET">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Période</label>
                        <select class="form-select" name="period" id="period">
                            <option value="today" {% if period == 'today' %}selected{% endif %}>Aujourd'hui</option>
                            <option value="week" {% if period == 'week' %}selected{% endif %}>Cette semaine</option>
                            <option value="month" {% if period == 'month' %}selected{% endif %}>Ce mois</option>
                            <option value="year" {% if period == 'year' %}selected{% endif %}>Cette année</option>
                            <option value="custom" {% if period == 'custom' %}selected{% endif %}>Personnalisé</option>
                        </select>
                    </div>
                    <div id="customDateRange" class="{% if period != 'custom' %}d-none{% endif %}">
                        <div class="mb-3">
                            <label class="form-label">Date de début</label>
                            <input type="date" class="form-control" name="start_date" value="{{ start_date | datetime_auto('date') }}">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Date de fin</label>
                            <input type="date" class="form-control" name="end_date" value="{{ end_date | datetime_auto('date') }}">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Appliquer</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // DataTable initialization
    $('#salesTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
        },
        "order": [[0, "desc"]],
        "columnDefs": [
            { "type": "date", "targets": 0 }
        ]
    });
    $('#onlineSalesTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
        },
        "order": [[0, "desc"]],
        "columnDefs": [
            { "type": "date", "targets": 0 }
        ]
    });
    // Tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();

    // Sales Trend Chart
    var ctx = document.getElementById('salesTrendChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: {{ dates|tojson }},
            datasets: [{
                label: 'Ventes',
                data: {{ sales_values|tojson }},
                borderColor: 'rgb(78, 115, 223)',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' });
                        }
                    }
                }
            }
        }
    });

    // Top Products Chart
    var ctx2 = document.getElementById('topProductsChart').getContext('2d');
    new Chart(ctx2, {
        type: 'doughnut',
        data: {
            labels: {{ product_labels|tojson }},
            datasets: [{
                data: {{ product_data|tojson }},
                backgroundColor: {{ product_colors|tojson }},
                hoverOffset: 4
            }]
        },
        options: {
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let value = context.raw;
                            return value.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' });
                        }
                    }
                }
            }
        }
    });

    // Répartition ventes POS vs Online
    var ctx3 = document.getElementById('salesSourceChart').getContext('2d');
    new Chart(ctx3, {
        type: 'doughnut',
        data: {
            labels: ['POS', 'Online'],
            datasets: [{
                data: [{{ pos_revenue|default(0) }}, {{ online_revenue|default(0) }}],
                backgroundColor: ['rgba(78, 115, 223, 0.8)', 'rgba(246, 194, 62, 0.8)'],
                hoverOffset: 4
            }]
        },
        options: {
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let value = context.raw;
                            return value.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' });
                        }
                    }
                }
            }
        }
    });

    // Handle period selection
    $('#period').change(function() {
        if ($(this).val() === 'custom') {
            $('#customDateRange').removeClass('d-none');
        } else {
            $('#customDateRange').addClass('d-none');
        }
    });
});

function viewSale(id) {
    window.location.href = `/pos/sales/${id}`;
}

function printReceipt(id) {
    window.open(`/pos/sales/${id}/receipt`, '_blank');
}

function exportSalesReport() {
    // Get current filters
    const period = document.getElementById('period').value;
    
    // Build URL with parameters
    let url = new URL(`${window.location.origin}/reports/sales/export`);
    url.searchParams.set('period', period);
    
    if (period === 'custom') {
        const startDate = document.querySelector('input[name="start_date"]').value;
        const endDate = document.querySelector('input[name="end_date"]').value;
        url.searchParams.set('start_date', startDate);
        url.searchParams.set('end_date', endDate);
    }
    
    // Trigger download
    window.location.href = url.toString();
}
</script>
{% endblock %} 
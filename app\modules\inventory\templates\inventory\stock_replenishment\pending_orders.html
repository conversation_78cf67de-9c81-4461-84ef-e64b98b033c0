{% extends "base.html" %}

{% block title %}Commandes en Attente - Pending Marchandise{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
<script src="{{ url_for('inventory.static', filename='js/pending_orders.js') }}"></script>
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-3">
                <h2 class="mb-0">
                    <i class="fas fa-clock text-warning"></i> 
                    Commandes en Attente - Pending Marchandise
                </h2>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-plus"></i> Nouvelle Commande
                    </a>
                    <a href="{{ url_for('inventory.purchase_orders_list') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-list"></i> Toutes les Commandes
                    </a>
                    <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Statistiques rapides -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card bg-warning">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ pending_orders|length }}</h3>
                        <p>Commandes en attente</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-info">
                    <div class="stat-icon">
                        <i class="fas fa-euro-sign"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ "%.2f"|format(total_pending_amount) }} €</h3>
                        <p>Valeur totale</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-primary">
                    <div class="stat-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ total_pending_items }}</h3>
                        <p>Articles en attente</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-success">
                    <div class="stat-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ unique_suppliers }}</h3>
                        <p>Fournisseurs concernés</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtres et Recherche -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-filter"></i> Filtres et Recherche
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label class="form-label">Recherche rapide</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="searchInput"
                                   placeholder="Rechercher par référence, fournisseur, article...">
                            <button class="btn btn-outline-secondary" type="button" onclick="PendingOrders.clearSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">Fournisseur</label>
                        <select class="form-select" id="supplierFilter">
                            <option value="">Tous les fournisseurs</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Date de commande</label>
                        <input type="date" class="form-control" id="dateFilter">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Statut</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">Tous les statuts</option>
                            <option value="pending">En attente</option>
                            <option value="partial_received">Partiellement reçue</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Actions</label>
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary" onclick="PendingOrders.applyFilters()">
                                <i class="fas fa-filter"></i> Filtrer
                            </button>
                            <button class="btn btn-outline-secondary" onclick="PendingOrders.clearFilters()">
                                <i class="fas fa-times"></i> Effacer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Liste des commandes en attente -->
        <div class="row">
            {% if pending_orders %}
                {% for order in pending_orders %}
                <div class="col-lg-6 mb-4 order-card"
                     data-order-id="{{ order.id }}"
                     data-supplier="{{ order.supplier_id or 0 }}"
                     data-date="{{ order.order_date | datetime_auto('date') }}"
                     data-status="{{ order.status.value }}"
                     data-reference="{{ order.reference }}"
                     data-supplier-name="{{ order.supplier_name if order.supplier_name else 'Aucun fournisseur' }}">
                    <div class="card h-100 {% if order.status.value == 'pending' %}border-warning{% else %}border-info{% endif %}">
                        <div class="card-header {% if order.status.value == 'pending' %}bg-warning bg-opacity-10{% else %}bg-info bg-opacity-10{% endif %}">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">
                                        <i class="fas fa-file-alt"></i>
                                        {{ order.reference }}
                                    </h6>
                                    <small class="text-muted">
                                        {% if order.status.value == 'pending' %}
                                            <span class="badge bg-warning text-dark">En attente</span>
                                        {% elif order.status.value == 'partial_received' %}
                                            <span class="badge bg-info">Partiellement reçue</span>
                                        {% endif %}
                                    </small>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item"
                                               href="{{ url_for('inventory.purchase_order_details', order_id=order.id) }}">
                                                <i class="fas fa-eye"></i> Voir détails
                                            </a>
                                        </li>
                                        <li>
                                            <button class="dropdown-item" onclick="PendingOrders.markAsReceived({{ order.id }})">
                                                <i class="fas fa-check"></i> Marquer comme reçue
                                            </button>
                                        </li>
                                        <li>
                                            <button class="dropdown-item" onclick="PendingOrders.editOrder({{ order.id }})">
                                                <i class="fas fa-edit"></i> Modifier
                                            </button>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <button class="dropdown-item text-danger" onclick="PendingOrders.cancelOrder({{ order.id }})">
                                                <i class="fas fa-times"></i> Annuler
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Informations de base -->
                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>Fournisseur:</strong><br>
                                    <span class="text-primary">{{ order.supplier_name }}</span>
                                </div>
                                <div class="col-6">
                                    <strong>Date de commande:</strong><br>
                                    {{ order.order_date | datetime_auto('date') }}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>Montant total:</strong><br>
                                    <span class="h6 text-success">{{ "%.2f"|format(order.total_amount) }} €</span>
                                </div>
                                <div class="col-6">
                                    <strong>Statut:</strong><br>
                                    {% if order.status.value == 'pending' %}
                                        <span class="badge bg-warning">En attente</span>
                                    {% elif order.status.value == 'partial_received' %}
                                        <span class="badge bg-info">Partiellement reçue</span>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Articles de la commande -->
                            <div class="mb-3">
                                <strong>Articles ({{ order.items.count() }}):</strong>
                                <div class="mt-2">
                                    {% for item in order.items[:3] %}
                                    <div class="d-flex justify-content-between align-items-center border-bottom py-1">
                                        <span class="small">{{ item.item_name }}</span>
                                        <span class="small text-muted">{{ item.quantity }} × {{ "%.2f"|format(item.unit_price) }}€</span>
                                    </div>
                                    {% endfor %}
                                    {% if order.items.count() > 3 %}
                                    <div class="text-center mt-2">
                                        <small class="text-muted">... et {{ order.items.count() - 3 }} autre(s) article(s)</small>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Date de livraison prévue -->
                            {% if order.expected_delivery_date %}
                            <div class="mb-3">
                                <strong>Livraison prévue:</strong><br>
                                <span class="text-info">{{ order.expected_delivery_date | datetime_auto('date') }}</span>
                                {% if order.expected_delivery_date < now.date() %}
                                    <span class="badge bg-danger ms-2">En retard</span>
                                {% endif %}
                            </div>
                            {% endif %}

                            <!-- Notes -->
                            {% if order.notes %}
                            <div class="mb-3">
                                <strong>Notes:</strong><br>
                                <small class="text-muted">{{ order.notes[:100] }}{% if order.notes|length > 100 %}...{% endif %}</small>
                            </div>
                            {% endif %}

                            <!-- Actions -->
                            <div class="d-flex flex-wrap gap-2">
                                {% if order.status.value == 'pending' %}
                                <button class="btn btn-success btn-sm" onclick="PendingOrders.markAsReceived({{ order.id }})"
                                        title="Marquer comme entièrement reçue">
                                    <i class="fas fa-check"></i> Marquer reçue
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="PendingOrders.partialReceive({{ order.id }})"
                                        title="Réception partielle des articles">
                                    <i class="fas fa-boxes"></i> Réception partielle
                                </button>
                                <button class="btn btn-outline-warning btn-sm" onclick="PendingOrders.editOrder({{ order.id }})"
                                        title="Modifier la commande">
                                    <i class="fas fa-edit"></i> Modifier
                                </button>
                                {% elif order.status.value == 'partial_received' %}
                                <button class="btn btn-success btn-sm" onclick="PendingOrders.markAsReceived({{ order.id }})"
                                        title="Marquer comme entièrement reçue">
                                    <i class="fas fa-check"></i> Finaliser réception
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="PendingOrders.partialReceive({{ order.id }})"
                                        title="Continuer la réception partielle">
                                    <i class="fas fa-boxes"></i> Continuer réception
                                </button>
                                {% endif %}
                                <a href="{{ url_for('inventory.purchase_order_details', order_id=order.id) }}"
                                   class="btn btn-outline-info btn-sm" title="Voir les détails complets">
                                    <i class="fas fa-eye"></i> Détails
                                </a>
                            </div>
                        </div>
                        <div class="card-footer text-muted">
                            <small>
                                Créée le {{ order.created_at | datetime_auto('full') }}                            {% if order.updated_at != order.created_at %}
                                    • Modifiée le {{ order.updated_at | datetime_auto('full') }}                            {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-clipboard-check fa-4x text-success mb-3"></i>
                            <h4 class="text-muted">Aucune commande en attente</h4>
                            <p class="text-muted mb-4">
                                Excellente nouvelle ! Toutes vos commandes ont été traitées.<br>
                                Vous pouvez créer une nouvelle commande si nécessaire.
                            </p>
                            <div class="d-flex justify-content-center gap-3">
                                <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Créer une nouvelle commande
                                </a>
                                <a href="{{ url_for('inventory.purchase_orders_list') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-list"></i> Voir toutes les commandes
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal de réception partielle -->
<div class="modal fade" id="partialReceiveModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-boxes"></i> Réception Partielle de Commande
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="partialReceiveContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-2 text-muted">Chargement des détails de la commande...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Annuler
                </button>
                <button type="button" class="btn btn-success" id="confirmPartialReceiveBtn">
                    <i class="fas fa-check"></i> Confirmer la réception
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation d'annulation -->
<div class="modal fade" id="cancelOrderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle"></i> Confirmer l'annulation
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir annuler cette commande ?</p>
                <p class="text-muted small">Cette action ne peut pas être annulée.</p>
                <div id="cancelOrderDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Non, garder la commande
                </button>
                <button type="button" class="btn btn-danger" id="confirmCancelBtn">
                    <i class="fas fa-trash"></i> Oui, annuler la commande
                </button>
            </div>
        </div>
    </div>
</div>


{% endblock %}

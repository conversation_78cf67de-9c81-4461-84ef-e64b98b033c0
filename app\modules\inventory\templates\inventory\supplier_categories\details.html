{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header" style="background-color: {{ category.color }}; color: white;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="mb-0">
                            <i class="{{ category.icon }}"></i> {{ category.name }}
                        </h2>
                        <div>
                            <a href="{{ url_for('inventory.edit_supplier_category', id=category.id) }}" 
                               class="btn btn-light btn-sm">
                                <i class="fas fa-edit"></i> Modifier
                            </a>
                            <a href="{{ url_for('inventory.supplier_categories') }}" 
                               class="btn btn-outline-light btn-sm">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if category.description %}
                        <div class="mb-4">
                            <h5>Description</h5>
                            <p>{{ category.description }}</p>
                        </div>
                    {% endif %}

                    <div class="mb-4">
                        <h5>Fournisseurs dans cette catégorie ({{ suppliers|length }})</h5>
                        {% if suppliers %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Nom</th>
                                            <th>Contact</th>
                                            <th>Email</th>
                                            <th>Téléphone</th>
                                            <th>Note</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for supplier in suppliers %}
                                        <tr>
                                            <td>{{ supplier.name }}</td>
                                            <td>{{ supplier.contact_name or '-' }}</td>
                                            <td>{{ supplier.email or '-' }}</td>
                                            <td>{{ supplier.phone or '-' }}</td>
                                            <td>
                                                {% if supplier.rating > 0 %}
                                                    <span class="text-warning">{{ supplier.display_rating }}</span>
                                                {% else %}
                                                    <span class="text-muted">Non noté</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{{ url_for('inventory.supplier_details', id=supplier.id) }}" 
                                                       class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ url_for('inventory.edit_supplier', id=supplier.id) }}" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-truck fa-2x text-muted mb-3"></i>
                                <p class="text-muted">Aucun fournisseur dans cette catégorie</p>
                                <a href="{{ url_for('inventory.add_supplier') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Ajouter un fournisseur
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="card-footer text-muted">
                    <small>
                        Créée le {{ category.created_at | datetime_auto('full') }}                    {% if category.updated_at != category.created_at %}
                            • Modifiée le {{ category.updated_at | datetime_auto('full') }}                    {% endif %}
                    </small>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Informations de la catégorie -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Informations</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <th>Nom</th>
                            <td>{{ category.name }}</td>
                        </tr>
                        <tr>
                            <th>Couleur</th>
                            <td>
                                <span class="badge" style="background-color: {{ category.color }};">
                                    {{ category.color }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th>Icône</th>
                            <td>
                                <i class="{{ category.icon }}"></i> {{ category.icon }}
                            </td>
                        </tr>
                        <tr>
                            <th>Fournisseurs</th>
                            <td>{{ suppliers|length }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('inventory.edit_supplier_category', id=category.id) }}" 
                           class="btn btn-warning">
                            <i class="fas fa-edit"></i> Modifier la catégorie
                        </a>
                        <a href="{{ url_for('inventory.add_supplier') }}?category_id={{ category.id }}" 
                           class="btn btn-primary">
                            <i class="fas fa-plus"></i> Ajouter un fournisseur
                        </a>
                        {% if suppliers|length == 0 %}
                        <button type="button" class="btn btn-outline-danger" 
                                onclick="confirmDelete({{ category.id }}, '{{ category.name }}')">
                            <i class="fas fa-trash"></i> Supprimer la catégorie
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la catégorie <strong id="categoryName"></strong> ?</p>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    Cette action ne peut pas être annulée.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteForm" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(categoryId, categoryName) {
    document.getElementById('categoryName').textContent = categoryName;
    document.getElementById('deleteForm').action = `/inventory/supplier-categories/${categoryId}/delete`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}

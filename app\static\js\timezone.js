/**
 * Gestion des fuseaux horaires côté client
 * Approche hybride : conversion JavaScript + fallback serveur
 */

class TimezoneManager {
    constructor() {
        this.userTimezone = null;
        this.dateFormat = 'fr-FR';
        this.init();
    }

    /**
     * Initialise le gestionnaire de fuseau horaire
     */
    init() {
        // Récupérer le fuseau horaire depuis les paramètres utilisateur ou détecter automatiquement
        this.userTimezone = this.getUserTimezone();

        // Afficher le fuseau horaire détecté dans la console pour debug
        console.log('Fuseau horaire utilisé:', this.userTimezone);

        // Convertir toutes les dates existantes sur la page
        this.convertAllDates();

        // Observer les nouvelles dates ajoutées dynamiquement
        this.observeNewDates();

        // Appliquer automatiquement le fuseau horaire détecté si configuré en UTC
        this.autoApplyDetectedTimezone();
    }

    /**
     * Récupère le fuseau horaire utilisateur
     */
    getUserTimezone() {
        // 1. Essayer de récupérer depuis les paramètres utilisateur (meta tag)
        const userTz = document.querySelector('meta[name="user-timezone"]');
        if (userTz && userTz.content && userTz.content !== 'UTC') {
            return userTz.content;
        }

        // 2. Si configuré en UTC, détecter automatiquement
        if (userTz && userTz.content === 'UTC') {
            try {
                const detectedTz = Intl.DateTimeFormat().resolvedOptions().timeZone;
                console.log('Fuseau horaire détecté automatiquement:', detectedTz);
                return detectedTz;
            } catch (e) {
                console.warn('Impossible de détecter le fuseau horaire:', e);
                return 'Europe/Paris';
            }
        }

        // 3. Essayer de récupérer depuis le localStorage
        const storedTz = localStorage.getItem('userTimezone');
        if (storedTz) {
            return storedTz;
        }

        // 4. Détecter automatiquement par défaut
        try {
            return Intl.DateTimeFormat().resolvedOptions().timeZone;
        } catch (e) {
            // Fallback pour les navigateurs plus anciens
            return 'Europe/Paris';
        }
    }

    /**
     * Formate une date UTC vers le fuseau horaire utilisateur
     */
    formatUserDate(utcDateString, options = {}) {
        if (!utcDateString) return '';

        try {
            // Créer un objet Date à partir de la chaîne UTC
            const utcDate = new Date(utcDateString);
            
            // Vérifier si la date est valide
            if (isNaN(utcDate.getTime())) {
                return utcDateString; // Retourner la chaîne originale si invalide
            }

            // Options par défaut
            const defaultOptions = {
                timeZone: this.userTimezone,
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            };

            // Fusionner avec les options personnalisées
            const formatOptions = { ...defaultOptions, ...options };

            // Formater selon la locale française
            return new Intl.DateTimeFormat('fr-FR', formatOptions).format(utcDate);
        } catch (error) {
            console.warn('Erreur lors du formatage de la date:', error);
            return utcDateString; // Fallback
        }
    }

    /**
     * Formate une date pour affichage court (date seulement)
     */
    formatUserDateShort(utcDateString) {
        return this.formatUserDate(utcDateString, {
            hour: undefined,
            minute: undefined
        });
    }

    /**
     * Formate une heure pour affichage court (heure seulement)
     */
    formatUserTimeShort(utcDateString) {
        return this.formatUserDate(utcDateString, {
            year: undefined,
            month: undefined,
            day: undefined
        });
    }

    /**
     * Convertit toutes les dates sur la page
     */
    convertAllDates() {
        // Sélecteurs pour les éléments contenant des dates
        const dateSelectors = [
            '[data-utc-date]',
            '.utc-date',
            '.datetime-utc',
            '[data-datetime]'
        ];

        dateSelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(element => {
                if (!element.classList.contains('timezone-converted')) {
                    this.convertDateElement(element);
                }
            });
        });
    }

    /**
     * Convertit un élément de date spécifique
     */
    convertDateElement(element) {
        try {
            // Récupérer la date UTC depuis l'attribut data ou le contenu
            let utcDate = element.dataset.utcDate ||
                         element.dataset.datetime ||
                         element.textContent.trim();

            if (!utcDate) return;

            // Déterminer le type de formatage
            const formatType = element.dataset.format || 'full';
            let formattedDate;

            switch (formatType) {
                case 'date':
                    formattedDate = this.formatUserDateShort(utcDate);
                    break;
                case 'time':
                    formattedDate = this.formatUserTimeShort(utcDate);
                    break;
                case 'relative':
                    formattedDate = this.formatRelativeTime(utcDate);
                    break;
                default:
                    formattedDate = this.formatUserDate(utcDate);
            }

            // Mettre à jour le contenu de l'élément
            element.textContent = formattedDate;

            // Ajouter un titre avec la date UTC originale
            element.title = `UTC: ${utcDate}`;

            // Marquer comme converti pour éviter les reconversions
            element.classList.add('timezone-converted');
        } catch (error) {
            console.warn('Erreur lors de la conversion de date:', error, element);
        }
    }

    /**
     * Formate une date en temps relatif (il y a X minutes/heures/jours)
     */
    formatRelativeTime(utcDateString) {
        try {
            const utcDate = new Date(utcDateString);
            const now = new Date();
            const diffMs = now.getTime() - utcDate.getTime();
            const diffMinutes = Math.floor(diffMs / (1000 * 60));
            const diffHours = Math.floor(diffMinutes / 60);
            const diffDays = Math.floor(diffHours / 24);

            if (diffMinutes < 1) {
                return 'À l\'instant';
            } else if (diffMinutes < 60) {
                return `Il y a ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
            } else if (diffHours < 24) {
                return `Il y a ${diffHours} heure${diffHours > 1 ? 's' : ''}`;
            } else if (diffDays < 7) {
                return `Il y a ${diffDays} jour${diffDays > 1 ? 's' : ''}`;
            } else {
                // Pour les dates plus anciennes, afficher la date formatée
                return this.formatUserDate(utcDateString);
            }
        } catch (error) {
            return this.formatUserDate(utcDateString);
        }
    }

    /**
     * Observe les nouveaux éléments ajoutés au DOM
     */
    observeNewDates() {
        if (typeof MutationObserver !== 'undefined') {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Convertir les dates dans le nouvel élément
                            const dateElements = node.querySelectorAll('[data-utc-date], .utc-date, .datetime-utc, [data-datetime]');
                            dateElements.forEach(element => {
                                if (!element.classList.contains('timezone-converted')) {
                                    this.convertDateElement(element);
                                }
                            });
                        }
                    });
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }

    /**
     * Convertit une date locale vers UTC (pour les formulaires)
     */
    localToUTC(localDateString) {
        try {
            const localDate = new Date(localDateString);
            return localDate.toISOString();
        } catch (error) {
            console.warn('Erreur lors de la conversion vers UTC:', error);
            return localDateString;
        }
    }

    /**
     * Met à jour le fuseau horaire utilisateur
     */
    setUserTimezone(timezone) {
        this.userTimezone = timezone;
        localStorage.setItem('userTimezone', timezone);

        // Reconvertir toutes les dates
        document.querySelectorAll('.timezone-converted').forEach(element => {
            element.classList.remove('timezone-converted');
        });
        this.convertAllDates();
    }

    /**
     * Applique automatiquement le fuseau horaire détecté si configuré en UTC
     */
    autoApplyDetectedTimezone() {
        const userTzMeta = document.querySelector('meta[name="user-timezone"]');
        if (userTzMeta && userTzMeta.content === 'UTC') {
            try {
                const detectedTz = Intl.DateTimeFormat().resolvedOptions().timeZone;
                console.log('Fuseau horaire détecté automatiquement:', detectedTz);

                // Mettre à jour automatiquement le fuseau horaire
                this.userTimezone = detectedTz;
                localStorage.setItem('userTimezone', detectedTz);

                // Reconvertir toutes les dates avec le nouveau fuseau horaire
                document.querySelectorAll('.timezone-converted').forEach(element => {
                    element.classList.remove('timezone-converted');
                });
                this.convertAllDates();

                console.log('Fuseau horaire appliqué automatiquement:', detectedTz);
            } catch (error) {
                console.warn('Impossible d\'appliquer automatiquement le fuseau horaire:', error);
            }
        }
    }
}

// Initialiser le gestionnaire de fuseau horaire quand le DOM est prêt
document.addEventListener('DOMContentLoaded', function() {
    window.timezoneManager = new TimezoneManager();
});

// Fonction globale pour formater les dates (utilisable depuis les templates)
window.formatUserDate = function(utcDate, format = 'full') {
    if (window.timezoneManager) {
        switch (format) {
            case 'date':
                return window.timezoneManager.formatUserDateShort(utcDate);
            case 'time':
                return window.timezoneManager.formatUserTimeShort(utcDate);
            case 'relative':
                return window.timezoneManager.formatRelativeTime(utcDate);
            default:
                return window.timezoneManager.formatUserDate(utcDate);
        }
    }
    return utcDate;
};

{% extends "base.html" %}

{% block content %}
<div class="container">
    <h1 class="mb-4">Gestion de Caisse</h1>

    {% if current_user.is_owner %}
    <div class="text-end mb-3">
        <a href="{{ url_for('cash_register.settings') }}" class="btn btn-outline-primary">
            <i class="fas fa-cog"></i> Paramètres de la caisse
        </a>
    </div>
    {% endif %}

    {% if current_register and current_register.is_open %}
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h3>État de la Caisse</h3>
                    <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#closeRegisterModal">
                        <i class="fas fa-door-closed"></i> <PERSON><PERSON>er la Caisse
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h5>Fond de Caisse Initial</h5>
                        <p class="h3"><span class="badge bg-primary">{{ format_currency(current_register.float_amount) }}</span></p>
                    </div>
                    <div class="col-md-3">
                        <h5>Chiffre d'Affaires</h5>
                        <p class="h3"><span class="badge bg-success">{{ format_currency(total_sales) }}</span></p>
                    </div>
                    <div class="col-md-3">
                        <h5>Solde Total</h5>
                        <p class="h3">
                            <span class="badge bg-info">
                                {{ format_currency(current_register.float_amount + total_sales + total_deposits - total_withdrawals) }}
                            </span>
                        </p>
                        <small class="text-muted">
                            (Fond initial + CA + Entrées - Sorties)
                        </small>
                    </div>
                    <div class="col-md-3">
                        <h5>Mouvements</h5>
                        <p>Entrées: <span class="text-success">+{{ format_currency(total_deposits) }}</span></p>
                        <p>Sorties: <span class="text-danger">-{{ format_currency(total_withdrawals) }}</span></p>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-3">
                        <h5>Espèces</h5>
                        <p class="h4">{{ format_currency(cash_total) }}</p>
                    </div>
                    <div class="col-md-3">
                        <h5>Carte Bancaire</h5>
                        <p class="h4">{{ format_currency(card_total) }}</p>
                    </div>
                    <div class="col-md-3">
                        <h5>Chèques</h5>
                        <p class="h4">{{ format_currency(check_total) }}</p>
                    </div>
                    <div class="col-md-3">
                        <h5>Autres</h5>
                        <p class="h4">{{ format_currency(other_total) }}</p>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#cashInModal">
                            <i class="fas fa-plus"></i> Entrée de Caisse
                        </button>
                        <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#cashOutModal">
                            <i class="fas fa-minus"></i> Sortie de Caisse
                        </button>
                        <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#bankDepositModal">
                            <i class="fas fa-university"></i> Versement en Banque
                        </button>
                        {% if current_user.is_owner %}
                        <button class="btn btn-secondary" data-bs-toggle="modal" data-bs-target="#resetRegisterModal">
                            <i class="fas fa-sync"></i> Réinitialiser
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Historique des mouvements -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Historique des Mouvements</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Montant</th>
                                <th>Mode de paiement</th>
                                <th>Note</th>
                                <th>Utilisateur</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if movements.items %}
                                {% for movement in movements.items %}
                                <tr>
                                    <td>{{ movement.date | datetime_auto('full') }}</td>
                                    <td>
                                        {% if movement.type == CashRegisterOperationType.SALE %}
                                            <span class="badge bg-success">Vente</span>
                                        {% elif movement.type == CashRegisterOperationType.CASH_IN %}
                                            <span class="badge bg-primary">Entrée</span>
                                        {% elif movement.type == CashRegisterOperationType.CASH_OUT %}
                                            <span class="badge bg-warning">Sortie</span>
                                        {% elif movement.type == CashRegisterOperationType.BANK_DEPOSIT %}
                                            <span class="badge bg-info">Dépôt bancaire</span>
                                        {% elif movement.type == CashRegisterOperationType.SUPPLIER_PAYMENT %}
                                            <span class="badge bg-secondary">Paiement fournisseur</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ format_currency(movement.amount) }}</td>
                                    <td>{{ movement.payment_method.value if movement.payment_method else '-' }}</td>
                                    <td>{{ movement.note }}</td>
                                    <td>{{ movement.user.username }}</td>
                                    <td>
                                        {% if movement.receipt_path %}
                                        <a href="{{ url_for('cash_register.download_receipt', operation_id=movement.id) }}"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-download"></i> Reçu
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">Aucun mouvement enregistré</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination des mouvements -->
                {% if movements.pages > 1 %}
                <nav aria-label="Navigation des mouvements">
                    <ul class="pagination justify-content-center">
                        {% if movements.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('cash_register.index', movements_page=movements.prev_num, register_ops_page=register_operations.page) }}">Précédent</a>
                        </li>
                        {% endif %}

                        {% for page in range(1, movements.pages + 1) %}
                        <li class="page-item {{ 'active' if page == movements.page else '' }}">
                            <a class="page-link" href="{{ url_for('cash_register.index', movements_page=page, register_ops_page=register_operations.page) }}">{{ page }}</a>
                        </li>
                        {% endfor %}

                        {% if movements.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('cash_register.index', movements_page=movements.next_num, register_ops_page=register_operations.page) }}">Suivant</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>

        <!-- Tableau spécifique aux achats de produits et ingrédients -->
        <div class="card mb-4">
            <div class="card-header">
                <h3><i class="fas fa-truck-loading text-success"></i> Achats de Produits et Ingrédients</h3>
                <small class="text-muted">Paiements effectués depuis la caisse pour l'approvisionnement</small>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Référence Commande</th>
                                <th>Fournisseur</th>
                                <th>Montant</th>
                                <th>Articles</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if supplier_payments %}
                                {% for payment in supplier_payments %}
                                <tr>
                                    <td>{{ payment.payment_date | datetime_auto('full') }}                                 <td>
                                        <a href="{{ url_for('inventory.purchase_order_details', order_id=payment.invoice.purchase_order.id) }}"
                                           class="text-decoration-none">
                                            {{ payment.invoice.purchase_order.reference }}
                                        </a>
                                    </td>
                                    <td>{{ payment.invoice.supplier.name if payment.invoice.supplier else 'Autres' }}</td>
                                    <td>
                                        <span class="text-danger">-{{ format_currency(payment.amount) }}</span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ payment.invoice.purchase_order.items.count() }} article(s)
                                        </small>
                                    </td>
                                    <td>
                                        {% if payment.invoice.payment_status.value == 'paid' %}
                                            <span class="badge bg-success">Payé</span>
                                        {% elif payment.invoice.payment_status.value == 'partial' %}
                                            <span class="badge bg-warning">Partiel</span>
                                        {% else %}
                                            <span class="badge bg-danger">En attente</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('inventory.purchase_order_details', order_id=payment.invoice.purchase_order.id) }}"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> Voir
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        <i class="fas fa-info-circle"></i> Aucun achat effectué depuis la caisse
                                    </td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>

                <!-- Résumé des achats -->
                {% if supplier_payments %}
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">Total des Achats (Période)</h6>
                                <h4 class="text-danger">-{{ format_currency(total_supplier_payments) }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">Nombre de Transactions</h6>
                                <h4 class="text-info">{{ supplier_payments|length }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Historique des ouvertures/fermetures -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Historique des Ouvertures/Fermetures</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Opération</th>
                                <th>Montant Initial</th>
                                <th>Montant Final</th>
                                <th>Utilisateur</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if register_operations.items %}
                                {% for operation in register_operations.items %}
                                <tr>
                                    <td>{{ operation.date | datetime_auto('full') }}</td>
                                    <td>
                                        {% if operation.type == CashRegisterOperationType.OPENING %}
                                            <span class="badge bg-success">Ouverture</span>
                                        {% else %}
                                            <span class="badge bg-danger">Fermeture</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ format_currency(operation.initial_amount) }}</td>
                                    <td>{{ format_currency(operation.final_amount) }}</td>
                                    <td>{{ operation.user.username }}</td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="5" class="text-center">Aucune opération enregistrée</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination des opérations d'ouverture/fermeture -->
                {% if register_operations.pages > 1 %}
                <nav aria-label="Navigation des opérations">
                    <ul class="pagination justify-content-center">
                        {% if register_operations.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('cash_register.index', register_ops_page=register_operations.prev_num, movements_page=movements.page) }}">Précédent</a>
                        </li>
                        {% endif %}

                        {% for page in range(1, register_operations.pages + 1) %}
                        <li class="page-item {{ 'active' if page == register_operations.page else '' }}">
                            <a class="page-link" href="{{ url_for('cash_register.index', register_ops_page=page, movements_page=movements.page) }}">{{ page }}</a>
                        </li>
                        {% endfor %}

                        {% if register_operations.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('cash_register.index', register_ops_page=register_operations.next_num, movements_page=movements.page) }}">Suivant</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    {% else %}
        <div class="card">
            <div class="card-body">
                <h3>La caisse est fermée</h3>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#openRegisterModal">
                    Ouvrir la Caisse
                </button>
            </div>
        </div>
    {% endif %}
</div>

<!-- Modals -->
{% include 'cash_register/_open_modal.html' %}
{% include 'cash_register/_close_modal.html' %}
{% include 'cash_register/_cash_in_modal.html' %}
{% include 'cash_register/_cash_out_modal.html' %}
{% include 'cash_register/_bank_deposit_modal.html' %}
{% include 'cash_register/_reset_modal.html' %}
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialiser les modaux Bootstrap
    var modals = document.querySelectorAll('.modal');
    modals.forEach(function(modal) {
        new bootstrap.Modal(modal);
    });

    // Fonction pour gérer la soumission des formulaires
    function handleFormSubmit(formId, modalId) {
        const form = document.getElementById(formId);
        if (!form) return; // Si le formulaire n'existe pas, on ne fait rien

        form.addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || 'Erreur serveur');
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
                    if (modal) {
                        modal._element.setAttribute('data-operation-success', 'true');
                        modal.hide();
                    }
                    if (data.message) {
                        alert(data.message);
                    }
                    window.location.reload();
                } else {
                    alert(data.error || 'Une erreur est survenue');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert(error.message || 'Une erreur est survenue lors de la communication avec le serveur');
            });
        });
    }

    // Gérer uniquement les formulaires qui existent sur la page
    if (document.getElementById('openRegisterForm')) {
        handleFormSubmit('openRegisterForm', 'openRegisterModal');
    }
    if (document.getElementById('closeRegisterForm')) {
        handleFormSubmit('closeRegisterForm', 'closeRegisterModal');
    }
});
</script>
{% endblock %}

{% extends "base.html" %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">{{ title }}</h1>
    
    <div class="row mt-4">
        <div class="col-xl-12">
            <!-- Paramètres généraux -->
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-cogs me-1"></i>
                    Paramètres généraux
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('admin.update_system_settings') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        
                        <!-- Informations de l'entreprise -->
                        <div class="mb-4">
                            <h5>Informations de l'entreprise</h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="businessName" class="form-label">Nom de l'entreprise</label>
                                    <input type="text" class="form-control" id="businessName" name="business_name"
                                           value="{{ settings.business_name if settings else '' }}" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="businessPhone" class="form-label">Téléphone</label>
                                    <input type="tel" class="form-control" id="businessPhone" name="business_phone"
                                           value="{{ settings.business_phone if settings else '' }}">
                                </div>
                                <div class="col-md-6">
                                    <label for="businessEmail" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="businessEmail" name="business_email"
                                           value="{{ settings.business_email if settings else '' }}">
                                </div>
                                <div class="col-md-6">
                                    <label for="businessAddress" class="form-label">Adresse</label>
                                    <input type="text" class="form-control" id="businessAddress" name="business_address"
                                           value="{{ settings.business_address if settings else '' }}">
                                </div>
                            </div>
                        </div>

                        <!-- Paramètres système -->
                        <div class="mb-4">
                            <h5>Paramètres système</h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="timezone" class="form-label">Fuseau horaire</label>
                                    <select class="form-select" id="timezone" name="timezone">
                                        <optgroup label="Europe">
                                            <option value="Europe/Paris" {{ 'selected' if settings and settings.timezone == 'Europe/Paris' }}>Europe/Paris (GMT+1/+2)</option>
                                            <option value="Europe/London" {{ 'selected' if settings and settings.timezone == 'Europe/London' }}>Europe/London (GMT+0/+1)</option>
                                            <option value="Europe/Berlin" {{ 'selected' if settings and settings.timezone == 'Europe/Berlin' }}>Europe/Berlin (GMT+1/+2)</option>
                                            <option value="Europe/Rome" {{ 'selected' if settings and settings.timezone == 'Europe/Rome' }}>Europe/Rome (GMT+1/+2)</option>
                                            <option value="Europe/Madrid" {{ 'selected' if settings and settings.timezone == 'Europe/Madrid' }}>Europe/Madrid (GMT+1/+2)</option>
                                            <option value="Europe/Amsterdam" {{ 'selected' if settings and settings.timezone == 'Europe/Amsterdam' }}>Europe/Amsterdam (GMT+1/+2)</option>
                                            <option value="Europe/Brussels" {{ 'selected' if settings and settings.timezone == 'Europe/Brussels' }}>Europe/Brussels (GMT+1/+2)</option>
                                            <option value="Europe/Zurich" {{ 'selected' if settings and settings.timezone == 'Europe/Zurich' }}>Europe/Zurich (GMT+1/+2)</option>
                                        </optgroup>
                                        <optgroup label="Amérique du Nord">
                                            <option value="America/New_York" {{ 'selected' if settings and settings.timezone == 'America/New_York' }}>America/New_York (GMT-5/-4)</option>
                                            <option value="America/Los_Angeles" {{ 'selected' if settings and settings.timezone == 'America/Los_Angeles' }}>America/Los_Angeles (GMT-8/-7)</option>
                                            <option value="America/Chicago" {{ 'selected' if settings and settings.timezone == 'America/Chicago' }}>America/Chicago (GMT-6/-5)</option>
                                            <option value="America/Denver" {{ 'selected' if settings and settings.timezone == 'America/Denver' }}>America/Denver (GMT-7/-6)</option>
                                            <option value="America/Toronto" {{ 'selected' if settings and settings.timezone == 'America/Toronto' }}>America/Toronto (GMT-5/-4)</option>
                                            <option value="America/Montreal" {{ 'selected' if settings and settings.timezone == 'America/Montreal' }}>America/Montreal (GMT-5/-4)</option>
                                        </optgroup>
                                        <optgroup label="Asie">
                                            <option value="Asia/Tokyo" {{ 'selected' if settings and settings.timezone == 'Asia/Tokyo' }}>Asia/Tokyo (GMT+9)</option>
                                            <option value="Asia/Shanghai" {{ 'selected' if settings and settings.timezone == 'Asia/Shanghai' }}>Asia/Shanghai (GMT+8)</option>
                                            <option value="Asia/Dubai" {{ 'selected' if settings and settings.timezone == 'Asia/Dubai' }}>Asia/Dubai (GMT+4)</option>
                                        </optgroup>
                                        <optgroup label="Océanie">
                                            <option value="Australia/Sydney" {{ 'selected' if settings and settings.timezone == 'Australia/Sydney' }}>Australia/Sydney (GMT+10/+11)</option>
                                        </optgroup>
                                        <optgroup label="Afrique">
                                            <option value="Africa/Cairo" {{ 'selected' if settings and settings.timezone == 'Africa/Cairo' }}>Africa/Cairo (GMT+2)</option>
                                            <option value="Africa/Casablanca" {{ 'selected' if settings and settings.timezone == 'Africa/Casablanca' }}>Africa/Casablanca (GMT+0/+1)</option>
                                        </optgroup>
                                        <optgroup label="Autres">
                                            <option value="UTC" {{ 'selected' if settings and settings.timezone == 'UTC' }}>UTC (GMT+0)</option>
                                        </optgroup>
                                    </select>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle"></i>
                                        Votre fuseau horaire actuel détecté : <span id="detected-timezone"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="currency" class="form-label">Devise</label>
                                    <select class="form-select" id="currency" name="currency">
                                        <option value="EUR" {{ 'selected' if settings and settings.currency == 'EUR' }}>Euro (€)</option>
                                        <option value="USD" {{ 'selected' if settings and settings.currency == 'USD' }}>Dollar ($)</option>
                                        <option value="GBP" {{ 'selected' if settings and settings.currency == 'GBP' }}>Livre Sterling (£)</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="dateFormat" class="form-label">Format de date</label>
                                    <select class="form-select" id="dateFormat" name="date_format">
                                        <option value="%d/%m/%Y" {{ 'selected' if settings and settings.date_format == '%d/%m/%Y' }}>DD/MM/YYYY</option>
                                        <option value="%Y-%m-%d" {{ 'selected' if settings and settings.date_format == '%Y-%m-%d' }}>YYYY-MM-DD</option>
                                        <option value="%m/%d/%Y" {{ 'selected' if settings and settings.date_format == '%m/%d/%Y' }}>MM/DD/YYYY</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="timeFormat" class="form-label">Format d'heure</label>
                                    <select class="form-select" id="timeFormat" name="time_format">
                                        <option value="%H:%M" {{ 'selected' if settings and settings.time_format == '%H:%M' }}>24h (HH:MM)</option>
                                        <option value="%I:%M %p" {{ 'selected' if settings and settings.time_format == '%I:%M %p' }}>12h (HH:MM AM/PM)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Paramètres de notification -->
                        <div class="mb-4">
                            <h5>Notifications</h5>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="emailNotifications" name="email_notifications"
                                       {{ 'checked' if settings and settings.email_notifications }}>
                                <label class="form-check-label" for="emailNotifications">
                                    Activer les notifications par email
                                </label>
                            </div>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="stockAlerts" name="stock_alerts"
                                       {{ 'checked' if settings and settings.stock_alerts }}>
                                <label class="form-check-label" for="stockAlerts">
                                    Alertes de stock bas
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Enregistrer les paramètres
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Détecter et afficher le fuseau horaire actuel de l'utilisateur
    try {
        const detectedTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const detectedElement = document.getElementById('detected-timezone');
        if (detectedElement) {
            detectedElement.textContent = detectedTimezone;

            // Ajouter un bouton pour utiliser le fuseau horaire détecté
            const timezoneSelect = document.getElementById('timezone');
            const useDetectedBtn = document.createElement('button');
            useDetectedBtn.type = 'button';
            useDetectedBtn.className = 'btn btn-sm btn-outline-primary ms-2';
            useDetectedBtn.innerHTML = '<i class="fas fa-location-arrow"></i> Utiliser';
            useDetectedBtn.title = 'Utiliser le fuseau horaire détecté automatiquement';

            useDetectedBtn.addEventListener('click', function() {
                // Chercher l'option correspondante dans le select
                const options = timezoneSelect.querySelectorAll('option');
                let found = false;

                for (let option of options) {
                    if (option.value === detectedTimezone) {
                        option.selected = true;
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    // Si le fuseau horaire détecté n'est pas dans la liste, l'ajouter
                    const newOption = document.createElement('option');
                    newOption.value = detectedTimezone;
                    newOption.textContent = detectedTimezone + ' (Détecté automatiquement)';
                    newOption.selected = true;

                    // L'ajouter dans le groupe "Autres"
                    const otherGroup = timezoneSelect.querySelector('optgroup[label="Autres"]');
                    if (otherGroup) {
                        otherGroup.appendChild(newOption);
                    } else {
                        timezoneSelect.appendChild(newOption);
                    }
                }

                // Mettre à jour le gestionnaire de fuseau horaire côté client
                if (window.timezoneManager) {
                    window.timezoneManager.setUserTimezone(detectedTimezone);
                }

                // Feedback visuel
                useDetectedBtn.innerHTML = '<i class="fas fa-check"></i> Appliqué';
                useDetectedBtn.classList.remove('btn-outline-primary');
                useDetectedBtn.classList.add('btn-success');

                setTimeout(() => {
                    useDetectedBtn.innerHTML = '<i class="fas fa-location-arrow"></i> Utiliser';
                    useDetectedBtn.classList.remove('btn-success');
                    useDetectedBtn.classList.add('btn-outline-primary');
                }, 2000);
            });

            detectedElement.parentNode.appendChild(useDetectedBtn);
        }
    } catch (error) {
        console.warn('Impossible de détecter le fuseau horaire:', error);
        const detectedElement = document.getElementById('detected-timezone');
        if (detectedElement) {
            detectedElement.textContent = 'Non détecté';
        }
    }

    // Prévisualisation en temps réel des formats de date/heure
    const dateFormatSelect = document.getElementById('dateFormat');
    const timeFormatSelect = document.getElementById('timeFormat');
    const timezoneSelect = document.getElementById('timezone');

    function updatePreview() {
        const now = new Date();
        const dateFormat = dateFormatSelect.value;
        const timeFormat = timeFormatSelect.value;
        const timezone = timezoneSelect.value;

        // Créer un aperçu de la date/heure formatée
        let preview = '';
        try {
            // Simuler le formatage (approximatif)
            if (dateFormat === '%d/%m/%Y') {
                preview += now.toLocaleDateString('fr-FR');
            } else if (dateFormat === '%Y-%m-%d') {
                preview += now.toISOString().split('T')[0];
            } else if (dateFormat === '%m/%d/%Y') {
                preview += now.toLocaleDateString('en-US');
            }

            preview += ' ';

            if (timeFormat === '%H:%M') {
                preview += now.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit', hour12: false });
            } else if (timeFormat === '%I:%M %p') {
                preview += now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true });
            }

            preview += ` (${timezone})`;
        } catch (error) {
            preview = 'Aperçu non disponible';
        }

        // Afficher l'aperçu
        let previewElement = document.getElementById('datetime-preview');
        if (!previewElement) {
            previewElement = document.createElement('div');
            previewElement.id = 'datetime-preview';
            previewElement.className = 'alert alert-info mt-3';
            previewElement.innerHTML = '<strong>Aperçu :</strong> <span id="preview-text"></span>';
            timeFormatSelect.parentNode.parentNode.appendChild(previewElement);
        }

        const previewText = document.getElementById('preview-text');
        if (previewText) {
            previewText.textContent = preview;
        }
    }

    // Mettre à jour l'aperçu lors des changements
    dateFormatSelect.addEventListener('change', updatePreview);
    timeFormatSelect.addEventListener('change', updatePreview);
    timezoneSelect.addEventListener('change', updatePreview);

    // Aperçu initial
    updatePreview();
});
</script>
{% endblock %}
{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row">
        <!-- Profile Information -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user"></i>
                        Informations du profil
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div class="avatar-circle mb-3">
                            <span class="initials">{{ current_user.username[0]|upper }}</span>
                        </div>
                        <h5 class="mb-0">{{ current_user.username }}</h5>
                        <p class="text-muted">{{ current_user.email }}</p>
                    </div>
                    <hr>
                    <div class="text-start">
                        <p><strong>Membre depuis:</strong> {{ current_user.created_at | datetime_auto('date') if current_user.created_at else 'Non disponible' }}</p>
                        <p><strong>Dernière connexion:</strong> {{ current_user.last_login | datetime_auto('full') if current_user.last_login else 'Jamais' }}</p>
                        <p><strong>Rôle:</strong> {{ current_user.role|title }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Profile -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit"></i>
                        Modifier le profil
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('main.profile') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                            {% for error in form.username.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                            {% for error in form.email.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <hr>
                        <h6 class="mb-3">Changer le mot de passe</h6>
                        
                        <div class="mb-3">
                            {{ form.current_password.label(class="form-label") }}
                            {{ form.current_password(class="form-control" + (" is-invalid" if form.current_password.errors else "")) }}
                            {% for error in form.current_password.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.new_password.label(class="form-label") }}
                            {{ form.new_password(class="form-control" + (" is-invalid" if form.new_password.errors else "")) }}
                            {% for error in form.new_password.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.confirm_password.label(class="form-label") }}
                            {{ form.confirm_password(class="form-control" + (" is-invalid" if form.confirm_password.errors else "")) }}
                            {% for error in form.confirm_password.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="d-grid gap-2">
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-shield-alt"></i>
                        Sécurité
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Sessions actives</h6>
                        <p class="text-muted">
                            Gérez vos sessions de connexion actives sur différents appareils.
                        </p>
                        <button class="btn btn-danger">
                            <i class="fas fa-sign-out-alt"></i>
                            Déconnecter toutes les autres sessions
                        </button>
                    </div>
                    
                    <hr>
                    
                    <div class="mb-3">
                        <h6>Suppression du compte</h6>
                        <p class="text-muted">
                            Une fois que vous supprimez votre compte, il n'y a pas de retour en arrière. Soyez certain.
                        </p>
                        <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteAccountModal">
                            <i class="fas fa-trash-alt"></i>
                            Supprimer mon compte
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Account Modal -->
<div class="modal fade" id="deleteAccountModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer votre compte ? Cette action est irréversible.</p>
                <form method="POST" action="{{ url_for('main.delete_account') }}">
                    <div class="mb-3">
                        <label class="form-label">Pour confirmer, tapez "SUPPRIMER"</label>
                        <input type="text" class="form-control" name="confirmation" required pattern="SUPPRIMER">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-danger">Supprimer définitivement</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 100px;
    height: 100px;
    background-color: #4e73df;
    border-radius: 50%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}
.initials {
    font-size: 40px;
    color: white;
    font-weight: bold;
}
</style>
{% endblock %} 
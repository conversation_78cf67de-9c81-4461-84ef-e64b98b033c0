{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ title }}</h1>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Modifier l'utilisateur {{ user.username }}</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        <div class="form-group">
                            <label>Nom d'utilisateur</label>
                            <input type="text" name="username" class="form-control" value="{{ user.username }}" required>
                        </div>
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" name="email" class="form-control" value="{{ user.email }}" required>
                        </div>
                        <div class="form-group">
                            <label>Nouveau mot de passe (laisser vide pour ne pas changer)</label>
                            <input type="password" name="password" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Rôle</label>
                            <select name="role" class="form-control" required>
                                {% for role in roles %}
                                <option value="{{ role }}" {% if user.role == role %}selected{% endif %}>
                                    {% if role == roles.SYSTEM_ADMIN %}
                                    Administrateur Système
                                    {% else %}
                                    Propriétaire
                                    {% endif %}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" {% if user.is_active %}checked{% endif %}>
                                <label class="custom-control-label" for="is_active">Compte actif</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <a href="{{ url_for('admin.system_users') }}" class="btn btn-secondary">Annuler</a>
                            <button type="submit" class="btn btn-primary">Enregistrer</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informations</h6>
                </div>
                <div class="card-body">
                    <p><strong>Créé le:</strong> {{ user.created_at | datetime_auto('full') }}                    <p><strong>Dernière connexion:</strong> 
                        {% if user.last_login %}
                        {{ user.last_login | datetime_auto('full') }}                      {% else %}
                        Jamais connecté
                        {% endif %}
                    </p>
                    {% if user.role == roles.OWNER %}
                    <hr>
                    <h6 class="font-weight-bold">Statistiques</h6>
                    <p><strong>Nombre d'utilisateurs:</strong> {{ user.sales_as_owner|length }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 
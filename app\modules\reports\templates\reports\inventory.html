{% extends "base.html" %}

{% block styles %}
<style>
.chart-area, .chart-pie {
    position: relative;
    height: 400px;
    margin: 20px 0;
}
</style>
{% endblock %}

{% block title %}Inventaire{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-file-export"></i> Exporter
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="exportInventoryReport('csv')">
                        <i class="fas fa-file-csv"></i> CSV
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportInventoryReport('pdf')">
                        <i class="fas fa-file-pdf"></i> PDF
                    </a></li>
                </ul>
            </div>

        </div>
    </div>

    <!-- Unified Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header" style="cursor: pointer;" onclick="toggleFilters()" id="filterHeader">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-filter"></i> Filtres d'inventaire
                        <small class="text-muted float-end" id="filterToggleHint">
                            <i class="fas fa-chevron-up" id="filterChevron"></i>
                        </small>
                    </h6>
                </div>
                <div class="card-body" id="filterSection">
                    <form id="inventoryFilterForm" method="GET">
                        <!-- Type d'articles -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <label class="form-label fw-bold">Type d'articles</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="item_types" value="products_no_recipe" id="products_no_recipe"
                                                   {% if not request.args.getlist('item_types') or 'products_no_recipe' in request.args.getlist('item_types') %}checked{% endif %}>
                                            <label class="form-check-label" for="products_no_recipe">
                                                <i class="fas fa-box text-primary"></i> Produits sans recette
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="item_types" value="products_with_recipe" id="products_with_recipe"
                                                   {% if not request.args.getlist('item_types') or 'products_with_recipe' in request.args.getlist('item_types') %}checked{% endif %}>
                                            <label class="form-check-label" for="products_with_recipe">
                                                <i class="fas fa-utensils text-success"></i> Produits avec recette
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="item_types" value="ingredients" id="ingredients"
                                                   {% if not request.args.getlist('item_types') or 'ingredients' in request.args.getlist('item_types') %}checked{% endif %}>
                                            <label class="form-check-label" for="ingredients">
                                                <i class="fas fa-leaf text-warning"></i> Ingrédients
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Autres filtres -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Catégorie</label>
                                <select name="category" class="form-select" id="categorySelect">
                                    <option value="">Toutes les catégories</option>
                                    <optgroup label="Catégories de produits" id="productCategoriesGroup">
                                        {% for category in product_categories %}
                                        <option value="product_{{ category.id }}" {% if request.args.get('category') == 'product_' + category.id|string %}selected{% endif %}>
                                            {{ category.name }}
                                        </option>
                                        {% endfor %}
                                    </optgroup>
                                    <optgroup label="Catégories d'ingrédients" id="ingredientCategoriesGroup">
                                        {% for category in ingredient_categories %}
                                        <option value="ingredient_{{ category.id }}" {% if request.args.get('category') == 'ingredient_' + category.id|string %}selected{% endif %}>
                                            {{ category.name }}
                                        </option>
                                        {% endfor %}
                                    </optgroup>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">État du stock</label>
                                <select name="stock_status" class="form-select">
                                    <option value="">Tous les états</option>
                                    <option value="low" {% if request.args.get('stock_status') == 'low' %}selected{% endif %}>Stock faible</option>
                                    <option value="out" {% if request.args.get('stock_status') == 'out' %}selected{% endif %}>Rupture de stock</option>
                                    <option value="normal" {% if request.args.get('stock_status') == 'normal' %}selected{% endif %}>Stock normal</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Période</label>
                                <select name="period" class="form-select" id="periodSelect">
                                    <option value="all" {% if request.args.get('period') == 'all' %}selected{% endif %}>Toute la période</option>
                                    <option value="today" {% if request.args.get('period') == 'today' %}selected{% endif %}>Aujourd'hui</option>
                                    <option value="week" {% if request.args.get('period') == 'week' %}selected{% endif %}>Cette semaine</option>
                                    <option value="month" {% if request.args.get('period') == 'month' %}selected{% endif %}>Ce mois</option>
                                    <option value="quarter" {% if request.args.get('period') == 'quarter' %}selected{% endif %}>Ce trimestre</option>
                                    <option value="year" {% if request.args.get('period') == 'year' %}selected{% endif %}>Cette année</option>
                                    <option value="custom" {% if request.args.get('period') == 'custom' %}selected{% endif %}>Période personnalisée</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Recherche</label>
                                <input type="text" name="search" class="form-control" placeholder="Nom de l'article..." value="{{ request.args.get('search', '') }}">
                            </div>
                        </div>

                        <!-- Dates personnalisées -->
                        <div class="row mb-3" id="customDateFields" style="display: {% if request.args.get('period') == 'custom' %}block{% else %}none{% endif %};">
                            <div class="col-md-6">
                                <label class="form-label">Date de début</label>
                                <input type="date" name="start_date" class="form-control" value="{{ request.args.get('start_date', '') }}">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Date de fin</label>
                                <input type="date" name="end_date" class="form-control" value="{{ request.args.get('end_date', '') }}">
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Appliquer les filtres
                                </button>
                                <a href="{{ url_for('reports.inventory') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo"></i> Réinitialiser tous les filtres
                                </a>
                                <button type="button" class="btn btn-outline-info" onclick="toggleFilters()">
                                    <i class="fas fa-eye-slash" id="toggleIcon"></i> <span id="toggleText">Masquer les filtres</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards Row -->
    <div class="row">
        <!-- Total Items Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total des articles</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ items_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Stock faible</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ low_stock_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Out of Stock Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Rupture de stock</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ out_of_stock_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Value Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Valeur totale</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_stock_value|format_currency }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-euro-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">État du stock</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="inventoryTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Catégorie</th>
                            <th>Stock actuel</th>
                            <th>Stock minimum</th>
                            <th>Valeur unitaire</th>
                            <th>Valeur totale</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in items.items %}
                        <tr>
                            <td>{{ item.name }}</td>
                            <td>{{ item.category }}</td>
                            <td>
                                {% if item.stock_quantity <= item.minimum_stock %}
                                <span class="text-danger">{{ item.stock_quantity }}</span>
                                {% else %}
                                {{ item.stock_quantity }}
                                {% endif %}
                            </td>
                            <td>{{ item.minimum_stock }}</td>
                            <td>{{ item.unit_price|format_currency }}</td>
                            <td>{{ item.total_value|format_currency }}</td>
                            <td>
                                <button type="button" class="btn btn-info btn-sm" onclick="showHistory('{{ item.id }}')">
                                    <i class="fas fa-history"></i>
                                </button>
                                {% if not (item.type == 'product' and item.has_recipe) %}
                                <button type="button" class="btn btn-primary btn-sm" onclick="adjustStock('{{ item.id }}', '{{ item.type }}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                {% else %}
                                <button type="button" class="btn btn-secondary btn-sm" disabled title="Stock calculé automatiquement selon la recette">
                                    <i class="fas fa-lock"></i>
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination pour l'état du stock -->
            {% if items.pages > 1 %}
            <nav aria-label="Pagination de l'inventaire" class="mt-3">
                <ul class="pagination justify-content-center">
                    {% if items.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('reports.inventory',
                            page=items.prev_num,
                            item_types=request.args.getlist('item_types'),
                            category=request.args.get('category'),
                            stock_status=request.args.get('stock_status'),
                            period=request.args.get('period'),
                            search=request.args.get('search'),
                            start_date=request.args.get('start_date'),
                            end_date=request.args.get('end_date'),
                            history_page=request.args.get('history_page')
                        ) }}">Précédent</a>
                    </li>
                    {% endif %}

                    {% for page_num in items.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != items.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('reports.inventory',
                                    page=page_num,
                                    item_types=request.args.getlist('item_types'),
                                    category=request.args.get('category'),
                                    stock_status=request.args.get('stock_status'),
                                    period=request.args.get('period'),
                                    search=request.args.get('search'),
                                    start_date=request.args.get('start_date'),
                                    end_date=request.args.get('end_date'),
                                    history_page=request.args.get('history_page')
                                ) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if items.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('reports.inventory',
                            page=items.next_num,
                            item_types=request.args.getlist('item_types'),
                            category=request.args.get('category'),
                            stock_status=request.args.get('stock_status'),
                            period=request.args.get('period'),
                            search=request.args.get('search'),
                            start_date=request.args.get('start_date'),
                            end_date=request.args.get('end_date'),
                            history_page=request.args.get('history_page')
                        ) }}">Suivant</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>

    <!-- Movement History -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Historique des mouvements récents</h6>
        </div>
        <div class="card-body">
            {% if movement_history %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Article</th>
                            <th>Type</th>
                            <th>Motif</th>
                            <th>Quantité</th>
                            <th>Date</th>
                            <th>Utilisateur</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for movement in movement_history %}
                        <tr>
                            <td>
                                <strong>
                                {% if movement.product %}
                                    {{ movement.product.name }}
                                {% elif movement.ingredient %}
                                    {{ movement.ingredient.name }}
                                {% endif %}
                                </strong>
                            </td>
                            <td>
                                {% if movement.type == 'in' %}
                                <span class="badge bg-success">
                                    <i class="fas fa-arrow-up"></i> Entrée
                                </span>
                                {% else %}
                                <span class="badge bg-danger">
                                    <i class="fas fa-arrow-down"></i> Sortie
                                </span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-secondary">
                                    {% if movement.reason == 'purchase' %}
                                        <i class="fas fa-shopping-cart"></i> Achat
                                    {% elif movement.reason == 'sale' %}
                                        <i class="fas fa-cash-register"></i> Vente
                                    {% elif movement.reason == 'loss' %}
                                        <i class="fas fa-exclamation-triangle"></i> Perte
                                    {% elif movement.reason == 'adjustment' %}
                                        <i class="fas fa-edit"></i> Ajustement
                                    {% elif movement.reason == 'recipe' %}
                                        <i class="fas fa-utensils"></i> Recette
                                    {% else %}
                                        <i class="fas fa-question"></i> {{ movement.reason|title }}
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                {% if movement.type == 'in' %}
                                <span class="text-success fw-bold">+{{ movement.quantity }}</span>
                                {% else %}
                                <span class="text-danger fw-bold">-{{ movement.quantity }}</span>
                                {% endif %}
                            </td>
                            <td>{{ movement.created_at | datetime_auto('full') }}                         <td>
                                {% if movement.user %}
                                    <span class="badge bg-info">{{ movement.user.username }}</span>
                                {% else %}
                                    <span class="text-muted">Système</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if movement.notes %}
                                    <span class="text-muted" title="{{ movement.notes }}">
                                        {{ movement.notes[:30] }}{% if movement.notes|length > 30 %}...{% endif %}
                                    </span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination pour l'historique -->
            {% if movement_history_pagination and movement_history_pagination.pages > 1 %}
            <nav aria-label="Pagination de l'historique" class="mt-3">
                <ul class="pagination pagination-sm justify-content-center">
                    {% if movement_history_pagination.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('reports.inventory',
                            history_page=movement_history_pagination.prev_num,
                            page=request.args.get('page'),
                            item_types=request.args.getlist('item_types'),
                            category=request.args.get('category'),
                            stock_status=request.args.get('stock_status'),
                            period=request.args.get('period'),
                            search=request.args.get('search'),
                            start_date=request.args.get('start_date'),
                            end_date=request.args.get('end_date')
                        ) }}">Précédent</a>
                    </li>
                    {% endif %}

                    {% for page_num in movement_history_pagination.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != movement_history_pagination.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('reports.inventory',
                                    history_page=page_num,
                                    page=request.args.get('page'),
                                    item_types=request.args.getlist('item_types'),
                                    category=request.args.get('category'),
                                    stock_status=request.args.get('stock_status'),
                                    period=request.args.get('period'),
                                    search=request.args.get('search'),
                                    start_date=request.args.get('start_date'),
                                    end_date=request.args.get('end_date')
                                ) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if movement_history_pagination.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('reports.inventory',
                            history_page=movement_history_pagination.next_num,
                            page=request.args.get('page'),
                            item_types=request.args.getlist('item_types'),
                            category=request.args.get('category'),
                            stock_status=request.args.get('stock_status'),
                            period=request.args.get('period'),
                            search=request.args.get('search'),
                            start_date=request.args.get('start_date'),
                            end_date=request.args.get('end_date')
                        ) }}">Suivant</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <p class="text-muted">Aucun mouvement enregistré pour la période sélectionnée</p>
            {% endif %}
        </div>
    </div>

    <!-- Most Active Items -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Articles les plus actifs</h6>
        </div>
        <div class="card-body">
            {% if active_items %}
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Mouvements</th>
                            <th>Activité</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in active_items %}
                        <tr>
                            <td>{{ item.name }}</td>
                            <td>{{ item.movements_count }}</td>
                            <td>
                                <div class="progress">
                                    <div class="progress-bar bg-info" role="progressbar" 
                                         style="width: {% if max_movements > 0 %}{{ (item.movements_count / max_movements * 100)|round }}{% else %}0{% endif %}%">
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p class="text-muted">Aucune donnée disponible</p>
            {% endif %}
        </div>
    </div>
</div>



<!-- Stock Adjustment Modal -->
<div class="modal fade" id="adjustStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ajuster le stock</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="adjustStockForm" method="POST" action="{{ url_for('reports.quick_stock_update') }}">
                {{ form.csrf_token }}
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Type de mouvement</label>
                        {{ form.movement_type(class="form-select") }}
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Quantité</label>
                        {{ form.quantity(class="form-control", type="number", min="1") }}
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Note</label>
                        {{ form.note(class="form-control", rows="3") }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- History Modal -->
<div class="modal fade" id="historyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Historique des mouvements</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="historyContent">
                    <!-- Content will be loaded dynamically -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- {{ super() }} -->
<!-- Scripts spécifiques à la page d'inventaire -->
<script src="{{ url_for('reports.static', filename='js/reports.js') }}"></script>
<script>
// Fonction pour masquer/afficher les filtres
function toggleFilters() {
    const filterSection = document.getElementById('filterSection');
    const toggleIcon = document.getElementById('toggleIcon');
    const toggleText = document.getElementById('toggleText');
    const filterChevron = document.getElementById('filterChevron');

    if (filterSection.style.display === 'none') {
        filterSection.style.display = 'block';
        if (toggleIcon) {
            toggleIcon.className = 'fas fa-eye-slash';
            toggleText.textContent = 'Masquer les filtres';
        }
        if (filterChevron) {
            filterChevron.className = 'fas fa-chevron-up';
        }
    } else {
        filterSection.style.display = 'none';
        if (toggleIcon) {
            toggleIcon.className = 'fas fa-eye';
            toggleText.textContent = 'Afficher les filtres';
        }
        if (filterChevron) {
            filterChevron.className = 'fas fa-chevron-down';
        }
    }
}

// Fonction pour filtrer les catégories selon les types d'articles sélectionnés
function updateCategoryFilter() {
    const itemTypes = [];
    document.querySelectorAll('input[name="item_types"]:checked').forEach(checkbox => {
        itemTypes.push(checkbox.value);
    });

    const productGroup = document.getElementById('productCategoriesGroup');
    const ingredientGroup = document.getElementById('ingredientCategoriesGroup');

    // Afficher/masquer les groupes selon les types sélectionnés
    if (productGroup) {
        productGroup.style.display = (itemTypes.includes('products_no_recipe') || itemTypes.includes('products_with_recipe')) ? 'block' : 'none';
    }

    if (ingredientGroup) {
        ingredientGroup.style.display = itemTypes.includes('ingredients') ? 'block' : 'none';
    }

    // Si aucun type n'est sélectionné, afficher tous les groupes
    if (itemTypes.length === 0) {
        if (productGroup) productGroup.style.display = 'block';
        if (ingredientGroup) ingredientGroup.style.display = 'block';
    }
}

// Gestion des dates personnalisées et des filtres dynamiques
document.addEventListener('DOMContentLoaded', function() {
    const periodSelect = document.getElementById('periodSelect');
    const customDateFields = document.getElementById('customDateFields');

    // Gestion des dates personnalisées
    if (periodSelect && customDateFields) {
        periodSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                customDateFields.style.display = 'block';
            } else {
                customDateFields.style.display = 'none';
            }
        });
    }

    // Gestion des filtres de catégories dynamiques
    const checkboxes = document.querySelectorAll('input[name="item_types"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateCategoryFilter();
        });
    });

    // Initialiser le filtre de catégories au chargement
    updateCategoryFilter();

    // Améliorer l'UX du header cliquable
    const filterHeader = document.getElementById('filterHeader');
    if (filterHeader) {
        filterHeader.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fc';
        });
        filterHeader.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    }
});
</script>
{% endblock %}
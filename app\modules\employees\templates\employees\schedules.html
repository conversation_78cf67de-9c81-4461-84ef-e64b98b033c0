{% extends "employees/base_hr.html" %}

{% block title %}Plannings - {{ employee.full_name }}{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-calendar me-2"></i>Plannings de {{ employee.full_name }}
    </h1>
    <div>
        <a href="{{ url_for('employees.new_schedule', id=employee.id) }}" class="btn btn-primary me-2">
            <i class="fas fa-plus me-1"></i>Nouveau Planning
        </a>
        <a href="{{ url_for('employees.detail', id=employee.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour
        </a>
    </div>
</div>

<!-- Statistiques -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ schedules|length }}</h4>
                        <p class="mb-0">Plannings Total</p>
                    </div>
                    <i class="fas fa-calendar fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ schedules|selectattr('is_recurring', 'equalto', True)|list|length }}</h4>
                        <p class="mb-0">Récurrents</p>
                    </div>
                    <i class="fas fa-repeat fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ schedules|selectattr('shift_type', 'equalto', 'MORNING')|list|length }}</h4>
                        <p class="mb-0">Matins</p>
                    </div>
                    <i class="fas fa-sun fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ schedules|selectattr('shift_type', 'equalto', 'EVENING')|list|length }}</h4>
                        <p class="mb-0">Soirs</p>
                    </div>
                    <i class="fas fa-moon fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Vue calendaire -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-calendar-week me-2"></i>Vue Hebdomadaire
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>Lundi</th>
                        <th>Mardi</th>
                        <th>Mercredi</th>
                        <th>Jeudi</th>
                        <th>Vendredi</th>
                        <th>Samedi</th>
                        <th>Dimanche</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        {% for day in range(7) %}
                        <td class="align-top" style="width: 14.28%; min-height: 100px;">
                            {% set day_schedules = schedules|selectattr('day_of_week', 'equalto', day)|list %}
                            {% for schedule in day_schedules %}
                            <div class="small mb-1">
                                <span class="badge bg-{{ 'primary' if schedule.shift_type == 'MORNING' else 'warning' if schedule.shift_type == 'AFTERNOON' else 'info' if schedule.shift_type == 'EVENING' else 'dark' }}">
                                    {{ schedule.start_time | datetime_auto('time') }} }} - {{ schedule.end_time | datetime_auto('time') }} }}
                                </span>
                                <br>
                                <small class="text-muted">{{ schedule.shift_type.replace('_', ' ').title() }}</small>
                            </div>
                            {% endfor %}
                        </td>
                        {% endfor %}
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Liste détaillée -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>Liste des Plannings
        </h5>
    </div>
    <div class="card-body p-0">
        {% if schedules %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Jour</th>
                        <th>Type de Shift</th>
                        <th>Horaires</th>
                        <th>Période</th>
                        <th>Récurrent</th>
                        <th>Pause</th>
                        <th>Notes</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for schedule in schedules %}
                    <tr>
                        <td>
                            <strong>
                                {% set days = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'] %}
                                {{ days[schedule.day_of_week] }}
                            </strong>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'primary' if schedule.shift_type == 'MORNING' else 'warning' if schedule.shift_type == 'AFTERNOON' else 'info' if schedule.shift_type == 'EVENING' else 'dark' }}">
                                {{ schedule.shift_type.replace('_', ' ').title() }}
                            </span>
                        </td>
                        <td>
                            <strong>{{ schedule.start_time | datetime_auto('time') }} }} - {{ schedule.end_time | datetime_auto('time') }} }}</strong>
                            <br>
                            <small class="text-muted">
                                {% set start_dt = schedule.start_time %}
                                {% set end_dt = schedule.end_time %}
                                {% if end_dt > start_dt %}
                                    {% set duration = (end_dt.hour * 60 + end_dt.minute) - (start_dt.hour * 60 + start_dt.minute) %}
                                {% else %}
                                    {% set duration = (24 * 60) - (start_dt.hour * 60 + start_dt.minute) + (end_dt.hour * 60 + end_dt.minute) %}
                                {% endif %}
                                {{ (duration // 60) }}h{{ '%02d'|format(duration % 60) }}
                            </small>
                        </td>
                        <td>
                            {{ schedule.start_date | datetime_auto('date') }}<br>
                            <small class="text-muted">au {{ schedule.end_date | datetime_auto('date') }}</small>
                        </td>
                        <td>
                            {% if schedule.is_recurring %}
                                <span class="badge bg-success">Oui</span>
                            {% else %}
                                <span class="badge bg-secondary">Non</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if schedule.break_duration %}
                                {{ schedule.break_duration }} min
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if schedule.notes %}
                                <span class="text-truncate" style="max-width: 100px;" title="{{ schedule.notes }}">
                                    {{ schedule.notes }}
                                </span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('employees.edit_schedule', schedule_id=schedule.id) }}" 
                                   class="btn btn-sm btn-outline-warning" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteSchedule({{ schedule.id }})" title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucun planning défini</h5>
            <p class="text-muted">Créez le premier planning pour cet employé.</p>
            <a href="{{ url_for('employees.new_schedule', id=employee.id) }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Créer un Planning
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
function deleteSchedule(scheduleId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce planning ?')) {
        fetch(`/employees/schedules/${scheduleId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors de la suppression : ' + data.message);
            }
        })
        .catch(error => {
            alert('Erreur : ' + error.message);
        });
    }
}
</script>
{% endblock %}

{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="mb-0">{{ promotion.name }}</h2>
                        <div>
                            <a href="{{ url_for('promotions.edit', id=promotion.id) }}" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Modifier
                            </a>
                            <form action="{{ url_for('promotions.delete', id=promotion.id) }}" method="POST" style="display: inline;">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <button type="submit" class="btn btn-danger" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette promotion ?')">
                                    <i class="fas fa-trash"></i> Supprimer
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Informations Générales</h5>
                            <table class="table">
                                <tr>
                                    <th>Type</th>
                                    <td>
                                        {% if promotion.promotion_type == 'percentage' %}
                                            Pourcentage ({{ promotion.value }}%)
                                        {% elif promotion.promotion_type == 'fixed' %}
                                            Montant fixe ({{ "%.2f"|format(promotion.value) }} €)
                                        {% else %}
                                            Achetez {{ promotion.buy_x }} obtenez {{ promotion.get_y }}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Statut</th>
                                    <td>
                                        <span class="badge {% if promotion.is_active %}bg-success{% else %}bg-danger{% endif %}">
                                            {{ 'Actif' if promotion.is_active else 'Inactif' }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Période</th>
                                    <td>
                                        Du {{ promotion.start_date | datetime_auto('full') }}                                        au {{ promotion.end_date | datetime_auto('full') }}                                  </td>
                                </tr>
                                <tr>
                                    <th>Description</th>
                                    <td>{{ promotion.description or '-' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Conditions</h5>
                            <table class="table">
                                <tr>
                                    <th>Montant minimum</th>
                                    <td>
                                        {% if promotion.min_purchase %}
                                            {{ "%.2f"|format(promotion.min_purchase) }} €
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Réduction maximum</th>
                                    <td>
                                        {% if promotion.max_discount %}
                                            {{ "%.2f"|format(promotion.max_discount) }} €
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Limite d'utilisation</th>
                                    <td>
                                        {% if promotion.usage_limit %}
                                            {{ promotion.current_usage }}/{{ promotion.usage_limit }}
                                        {% else %}
                                            Illimité ({{ promotion.current_usage }} utilisations)
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <h5>Produits Concernés</h5>
                            {% if promotion.products %}
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Nom</th>
                                                <th>Catégorie</th>
                                                <th>Prix normal</th>
                                                <th>Prix promotionnel</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for product in promotion.products %}
                                            <tr>
                                                <td>{{ product.name }}</td>
                                                <td>{{ product.category.name if product.category else '-' }}</td>
                                                <td>{{ "%.2f"|format(product.price) }} €</td>
                                                <td>
                                                    {% if promotion.promotion_type == 'percentage' %}
                                                        {{ "%.2f"|format(product.price * (1 - promotion.value/100)) }} €
                                                    {% elif promotion.promotion_type == 'fixed' %}
                                                        {{ "%.2f"|format(product.price - promotion.value) }} €
                                                    {% else %}
                                                        -
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <p class="text-muted">Aucun produit associé à cette promotion</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('promotions.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour à la liste
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Widget des statistiques -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Statistiques</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Nombre d'utilisations
                            <span class="badge bg-primary rounded-pill">
                                {{ promotion.current_usage }}
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Produits concernés
                            <span class="badge bg-primary rounded-pill">
                                {{ promotion.products|length }}
                            </span>
                        </li>
                        <li class="list-group-item">
                            Statut de validité
                            <br>
                            <small class="text-muted">
                                {% if promotion.is_valid() %}
                                    <span class="text-success">
                                        <i class="fas fa-check-circle"></i> Promotion valide
                                    </span>
                                {% else %}
                                    <span class="text-danger">
                                        <i class="fas fa-times-circle"></i> Promotion invalide
                                    </span>
                                    <br>
                                    {% if not promotion.is_active %}
                                        - Promotion désactivée
                                    {% endif %}
                                    {% if datetime.utcnow() < promotion.start_date %}
                                        - Pas encore commencée
                                    {% endif %}
                                    {% if datetime.utcnow() > promotion.end_date %}
                                        - Déjà terminée
                                    {% endif %}
                                    {% if promotion.usage_limit and promotion.current_usage >= promotion.usage_limit %}
                                        - Limite d'utilisation atteinte
                                    {% endif %}
                                {% endif %}
                            </small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}{% endblock %} 
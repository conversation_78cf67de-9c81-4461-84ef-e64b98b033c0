{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Réservations</h1>
        <a href="{{ url_for('tables.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour aux tables
        </a>
    </div>

    <div class="card mb-4">
        <div class="card-body">
            <form class="row g-3 align-items-end" method="GET">
                <div class="col-md-4">
                    <label for="date" class="form-label">Date</label>
                    <input type="date" class="form-control" id="date" name="date"
                           value="{{ selected_date }}" onchange="this.form.submit()">
                </div>
                <div class="col-md-4">
                    <label for="status" class="form-label">Statut</label>
                    <select class="form-control" id="status" name="status" onchange="this.form.submit()">
                        <option value="all" {% if status_filter == 'all' %}selected{% endif %}>Toutes</option>
                        <option value="upcoming" {% if status_filter == 'upcoming' %}selected{% endif %}>À venir</option>
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>En cours</option>
                        <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>Terminées</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filtrer
                    </button>
                    <a href="{{ url_for('tables.reservations') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Légende</h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <span class="badge bg-success">En cours</span>
                        Réservation active
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-warning">À venir</span>
                        Réservation future
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-secondary">Terminée</span>
                        Réservation passée
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-9">
            <div class="card">
                <div class="card-body">
                    {% if reservations %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Heure</th>
                                        <th>Table</th>
                                        <th>Client</th>
                                        <th>Personnes</th>
                                        <th>Durée</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for reservation in reservations %}
                                    <tr>
                                        <td>{{ reservation.reservation_date | datetime_auto('time') }} }}</td>
                                        <td>Table {{ reservation.table.number }}</td>
                                        <td>
                                            {{ reservation.customer_name }}<br>
                                            <small class="text-muted">{{ reservation.customer_phone }}</small>
                                        </td>
                                        <td>{{ reservation.number_of_guests }}</td>
                                        <td>{{ reservation.duration_minutes }} min</td>
                                        <td>
                                            {% if reservation.is_active() %}
                                                <span class="badge bg-success">En cours</span>
                                            {% elif reservation.reservation_date > now %}
                                                <span class="badge bg-warning">À venir</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Terminée</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ url_for('tables.reservation_details', id=reservation.id) }}"
                                                   class="btn btn-sm btn-info" title="Voir les détails">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                {% if reservation.reservation_date > now %}
                                                    <a href="{{ url_for('tables.edit_reservation', id=reservation.id) }}"
                                                       class="btn btn-sm btn-warning" title="Modifier">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger"
                                                            onclick="confirmCancel({{ reservation.id }})" title="Annuler">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">Aucune réservation pour cette date</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation d'annulation -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer l'annulation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Êtes-vous sûr de vouloir annuler cette réservation ?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Non</button>
                <form id="cancelForm" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Oui, annuler</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmCancel(reservationId) {
    const modal = new bootstrap.Modal(document.getElementById('cancelModal'));
    const form = document.getElementById('cancelForm');
    form.action = `/tables/reservations/${reservationId}/cancel`;
    modal.show();
}
</script>
{% endblock %}
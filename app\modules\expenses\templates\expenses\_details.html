<!-- Expense Information -->
<div class="row mb-4">
    <div class="col-md-6">
        <h6 class="font-weight-bold">Informations générales</h6>
        <table class="table table-sm">
            <tr>
                <th style="width: 150px">Date</th>
                <td>{{ expense.date | datetime_auto('date') }}</td>
            </tr>
            <tr>
                <th>Description</th>
                <td>{{ expense.description }}</td>
            </tr>
            <tr>
                <th>Catégorie</th>
                <td>
                    <span class="badge" style="background-color: {{ expense.category.color }}">
                        {{ expense.category.name }}
                    </span>
                </td>
            </tr>
            <tr>
                <th>Référence</th>
                <td>{{ expense.reference or '-' }}</td>
            </tr>
            <tr>
                <th>Mode de paiement</th>
                <td>
                    {% if expense.payment_method == 'cash' %}
                    <i class="fas fa-money-bill text-success"></i> Espèces
                    {% elif expense.payment_method == 'card' %}
                    <i class="fas fa-credit-card text-primary"></i> Carte
                    {% elif expense.payment_method == 'transfer' %}
                    <i class="fas fa-exchange-alt text-info"></i> Virement
                    {% elif expense.payment_method == 'check' %}
                    <i class="fas fa-money-check text-warning"></i> Chèque
                    {% else %}
                    <i class="fas fa-question-circle text-muted"></i> Autre
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>Statut</th>
                <td>
                    {% if expense.status == 'paid' %}
                    <span class="badge bg-success">Payé</span>
                    {% else %}
                    <span class="badge bg-warning">En attente</span>
                    {% endif %}
                </td>
            </tr>
            {% if expense.is_recurring %}
            <tr>
                <th>Récurrence</th>
                <td>
                    {% if expense.recurring_interval == 'monthly' %}
                    Mensuel (le {{ expense.recurring_day }})
                    {% elif expense.recurring_interval == 'quarterly' %}
                    Trimestriel (le {{ expense.recurring_day }})
                    {% elif expense.recurring_interval == 'yearly' %}
                    Annuel (le {{ expense.recurring_day }})
                    {% endif %}
                </td>
            </tr>
            {% endif %}
        </table>
    </div>
    <div class="col-md-6">
        <h6 class="font-weight-bold">Montants</h6>
        <table class="table table-sm">
            <tr>
                <th style="width: 150px">Montant HT</th>
                <td>{{ "%.2f"|format(expense.amount_ht) }} €</td>
            </tr>
            <tr>
                <th>TVA</th>
                <td>{{ "%.2f"|format(expense.amount_tax) }} €</td>
            </tr>
            <tr class="fw-bold">
                <th>Montant TTC</th>
                <td>{{ "%.2f"|format(expense.amount_ttc) }} €</td>
            </tr>
            {% if expense.tax_rates %}
            <tr>
                <th colspan="2">
                    <small class="text-muted">
                        Détail TVA:
                        {% for rate in expense.tax_rates %}
                        {{ rate.rate }}% ({{ "%.2f"|format(rate.amount) }} €){% if not loop.last %}, {% endif %}
                        {% endfor %}
                    </small>
                </th>
            </tr>
            {% endif %}
        </table>
    </div>
</div>

{% if expense.image_path %}
<!-- Receipt -->
<h6 class="font-weight-bold mb-3">Justificatif</h6>
<div class="text-center">
    {% if expense.image_path.endswith('.pdf') %}
    <div class="mb-3">
        <i class="fas fa-file-pdf fa-3x text-danger"></i>
        <p class="mt-2">Document PDF</p>
    </div>
    {% else %}
    <img src="{{ url_for('static', filename=expense.image_path) }}" 
         class="img-fluid mb-3" 
         alt="Justificatif"
         style="max-height: 400px;">
    {% endif %}
    <div>
        <a href="{{ url_for('static', filename=expense.image_path) }}" 
           class="btn btn-primary"
           target="_blank">
            <i class="fas fa-external-link-alt"></i>
            Voir en plein écran
        </a>
    </div>
</div>
{% endif %}

<!-- Actions -->
<div class="mt-4 text-end">
    {% if expense.status == 'pending' %}
    <form action="{{ url_for('expenses.mark_paid', id=expense.id) }}" method="POST" class="d-inline">
        <button type="submit" class="btn btn-success">
            <i class="fas fa-check"></i>
            Marquer comme payé
        </button>
    </form>
    {% endif %}
    <a href="{{ url_for('expenses.edit', id=expense.id) }}" class="btn btn-primary">
        <i class="fas fa-edit"></i>
        Modifier
    </a>
</div> 
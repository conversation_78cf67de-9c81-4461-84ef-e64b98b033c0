{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-tags text-primary"></i>
            {{ title }}
        </h1>
        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-download"></i> Exporter
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="exportCategoriesReport('pdf')">
                    <i class="fas fa-file-pdf text-danger"></i> PDF
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="exportCategoriesReport('excel')">
                    <i class="fas fa-file-excel text-success"></i> Excel
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="exportCategoriesReport('csv')">
                    <i class="fas fa-file-csv text-info"></i> CSV
                </a></li>
            </ul>
        </div>
    </div>

    <!-- Period Filter -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" class="row align-items-end">
                <div class="col-md-3">
                    <label class="form-label">Période</label>
                    <select name="period" class="form-select" id="period" onchange="this.form.submit()">
                        <option value="today" {% if period == 'today' %}selected{% endif %}>Aujourd'hui</option>
                        <option value="week" {% if period == 'week' %}selected{% endif %}>Cette semaine</option>
                        <option value="month" {% if period == 'month' %}selected{% endif %}>Ce mois</option>
                        <option value="quarter" {% if period == 'quarter' %}selected{% endif %}>Ce trimestre</option>
                        <option value="year" {% if period == 'year' %}selected{% endif %}>Cette année</option>
                        <option value="custom" {% if period == 'custom' %}selected{% endif %}>Personnalisé</option>
                    </select>
                </div>
                
                <div class="col-md-3" id="custom-dates" style="display: {% if period == 'custom' %}block{% else %}none{% endif %};">
                    <label class="form-label">Du</label>
                    <input type="date" name="start_date" class="form-control" value="{{ start_date | datetime_auto('date') if start_date }}">
                </div>
                
                <div class="col-md-3" id="custom-dates-end" style="display: {% if period == 'custom' %}block{% else %}none{% endif %};">
                    <label class="form-label">Au</label>
                    <input type="date" name="end_date" class="form-control" value="{{ end_date | datetime_auto('date') if end_date }}">
                </div>
                
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filtrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Chiffre d'affaires total
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ "%.2f"|format(total_revenue) }}€
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-euro-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Quantité vendue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_quantity }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Profit total
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ "%.2f"|format(total_profit) }}€
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Marge globale
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ "%.1f"|format(overall_margin) }}%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Categories Chart -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie"></i> Répartition par Catégorie
                    </h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie">
                        <canvas id="categoriesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Products Chart -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar"></i> Top 10 Produits
                    </h6>
                </div>
                <div class="card-body">
                    <div class="chart-bar">
                        <canvas id="productsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Performers -->
    <div class="row mb-4">
        <!-- Top Categories -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-trophy"></i> Top Catégories par CA
                    </h6>
                </div>
                <div class="card-body">
                    {% if top_categories %}
                        {% for cat_data in top_categories %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>{{ cat_data.category.name }}</strong>
                                <br>
                                <small class="text-muted">{{ cat_data.quantity_sold }} unités • Marge: {{ "%.1f"|format(cat_data.profit_margin) }}%</small>
                            </div>
                            <div class="text-end">
                                <span class="h6 text-primary">{{ "%.2f"|format(cat_data.revenue) }}€</span>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucune donnée disponible</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Top Products by Revenue -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-medal"></i> Top Produits par CA
                    </h6>
                </div>
                <div class="card-body">
                    {% if top_products %}
                        {% for prod_data in top_products[:10] %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>{{ prod_data.product.name }}</strong>
                                <br>
                                <small class="text-muted">{{ prod_data.quantity_sold }} unités • {{ "%.2f"|format(prod_data.average_price) }}€/unité</small>
                            </div>
                            <div class="text-end">
                                <span class="h6 text-primary">{{ "%.2f"|format(prod_data.revenue) }}€</span>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucune donnée disponible</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Top Products by Quantity -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-sort-amount-down"></i> Top Produits par Quantité
                    </h6>
                </div>
                <div class="card-body">
                    {% if top_products_quantity %}
                        {% for prod_data in top_products_quantity %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>{{ prod_data.product.name }}</strong>
                                <br>
                                <small class="text-muted">CA: {{ "%.2f"|format(prod_data.revenue) }}€ • {{ "%.2f"|format(prod_data.average_price) }}€/unité</small>
                            </div>
                            <div class="text-end">
                                <span class="h6 text-success">{{ prod_data.quantity_sold }} unités</span>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucune donnée disponible</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Tables -->
    <div class="row">
        <!-- Rapport par Catégorie -->
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-tags"></i> Rapport détaillé par Catégorie
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="categoriesTable">
                            <thead>
                                <tr>
                                    <th>Catégorie</th>
                                    <th>Produits</th>
                                    <th>Quantité vendue</th>
                                    <th>CA POS</th>
                                    <th>CA Online</th>
                                    <th>CA Total</th>
                                    <th>Coût</th>
                                    <th>Profit</th>
                                    <th>Marge %</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category in categories %}
                                {% set stats = category_stats[category.id] %}
                                <tr>
                                    <td>
                                        <strong>{{ category.name }}</strong>
                                        {% if category.description %}
                                        <br><small class="text-muted">{{ category.description }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ stats.products_count }}</td>
                                    <td>{{ stats.quantity_sold }}</td>
                                    <td>{{ "%.2f"|format(stats.pos_revenue) }}€</td>
                                    <td>{{ "%.2f"|format(stats.online_revenue) }}€</td>
                                    <td><strong>{{ "%.2f"|format(stats.revenue) }}€</strong></td>
                                    <td>{{ "%.2f"|format(stats.cost) }}€</td>
                                    <td>{{ "%.2f"|format(stats.profit) }}€</td>
                                    <td>
                                        <span class="badge bg-{% if stats.profit_margin >= 30 %}success{% elif stats.profit_margin >= 15 %}warning{% else %}danger{% endif %}">
                                            {{ "%.1f"|format(stats.profit_margin) }}%
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Rapport détaillé par Produit -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-box"></i> Rapport détaillé par Produit
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="productsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>Image</th>
                                    <th>Produit</th>
                                    <th>Catégorie</th>
                                    <th>Quantité Vendue</th>
                                    <th>Ventes POS</th>
                                    <th>Ventes En ligne</th>
                                    <th>Chiffre d'Affaires Total</th>
                                    <th>Coût Total</th>
                                    <th>Bénéfice</th>
                                    <th>Marge (%)</th>
                                    <th>Prix Moyen</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product_id, stats in product_stats.items() %}
                                <tr>
                                    <td>
                                        {% if stats.product.image_path %}
                                        <img src="{{ url_for('static', filename=stats.product.image_path) }}" alt="{{ stats.product.name }}"
                                             class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                        {% else %}
                                        <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center"
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-box text-muted"></i>
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td data-order="{{ stats.product.name }}">
                                        <strong>{{ stats.product.name }}</strong>
                                        {% if not stats.product.is_active %}
                                        <span class="badge bg-secondary ms-1">Inactif</span>
                                        {% endif %}
                                    </td>
                                    <td data-order="{{ stats.product.category.name if stats.product.category else '' }}">
                                        {% if stats.product.category %}
                                        <span class="badge bg-{{ category_badge_colors[stats.product.category.id] }}">{{ stats.product.category.name }}</span>
                                        {% else %}
                                        <span class="text-muted">Sans catégorie</span>
                                        {% endif %}
                                    </td>
                                    <td data-order="{{ stats.quantity_sold }}">
                                        <span class="fw-bold">{{ stats.quantity_sold }}</span>
                                        <small class="text-muted d-block">
                                            POS: {{ stats.pos_quantity }} | En ligne: {{ stats.online_quantity }}
                                        </small>
                                    </td>
                                    <td data-order="{{ stats.pos_revenue }}">
                                        <span class="text-success fw-bold">
                                            {{ "%.2f"|format(stats.pos_revenue) }} €
                                        </span>
                                        <small class="text-muted d-block">
                                            {{ stats.pos_quantity }} unité(s)
                                        </small>
                                    </td>
                                    <td data-order="{{ stats.online_revenue }}">
                                        <span class="text-warning fw-bold">
                                            {{ "%.2f"|format(stats.online_revenue) }} €
                                        </span>
                                        <small class="text-muted d-block">
                                            {{ stats.online_quantity }} unité(s)
                                        </small>
                                    </td>
                                    <td data-order="{{ stats.revenue }}">
                                        <span class="fw-bold text-primary">
                                            {{ "%.2f"|format(stats.revenue) }} €
                                        </span>
                                    </td>
                                    <td data-order="{{ stats.cost }}">
                                        <span class="text-danger">
                                            {{ "%.2f"|format(stats.cost) }} €
                                        </span>
                                    </td>
                                    <td data-order="{{ stats.profit }}">
                                        <span class="{% if stats.profit >= 0 %}text-success{% else %}text-danger{% endif %} fw-bold">
                                            {{ "%.2f"|format(stats.profit) }} €
                                        </span>
                                    </td>
                                    <td data-order="{{ stats.profit_margin }}">
                                        <span class="{% if stats.profit_margin >= 0 %}text-success{% else %}text-danger{% endif %}">
                                            {{ "%.1f"|format(stats.profit_margin) }}%
                                        </span>
                                        {% if stats.profit_margin >= 30 %}
                                        <i class="fas fa-arrow-up text-success ms-1" title="Excellente marge"></i>
                                        {% elif stats.profit_margin >= 15 %}
                                        <i class="fas fa-minus text-warning ms-1" title="Marge correcte"></i>
                                        {% elif stats.profit_margin >= 0 %}
                                        <i class="fas fa-arrow-down text-danger ms-1" title="Marge faible"></i>
                                        {% else %}
                                        <i class="fas fa-exclamation-triangle text-danger ms-1" title="Perte"></i>
                                        {% endif %}
                                    </td>
                                    <td data-order="{{ stats.average_price }}">
                                        <span class="text-info">
                                            {{ "%.2f"|format(stats.average_price) }} €
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Initialize DataTable for categories
    $('#categoriesTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
        },
        "order": [[5, "desc"]], // Trier par CA Total décroissant
        "pageLength": 25,
        "responsive": true
    });

    // Initialize DataTable for products
    $('#productsTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
        },
        "order": [[6, "desc"]], // Chiffre d'affaires total (nouvel index)
        "pageLength": 25,
        "responsive": true,
        "columnDefs": [
            { "type": "num-fmt", "targets": [3, 4, 5, 6, 7, 8, 10] }, // Colonnes numériques (décalées)
            { "orderable": false, "targets": [0] } // Image non triable
        ]
    });
    
    // Handle period change
    $('#period').change(function() {
        const period = $(this).val();
        if (period === 'custom') {
            $('#custom-dates, #custom-dates-end').show();
        } else {
            $('#custom-dates, #custom-dates-end').hide();
        }
    });
    
    // Categories Chart
    console.log('Top categories data:', {{ top_categories|length }});
    const categoriesData = [
        {% for cat_data in top_categories[:8] %}
        {
            label: '{{ cat_data.category.name }}',
            value: {{ cat_data.revenue }},
            color: 'hsl({{ loop.index0 * 45 }}, 70%, 60%)'
        },
        {% endfor %}
    ];
    console.log('Categories chart data:', categoriesData);
    
    if (categoriesData.length > 0) {
        const categoriesCtx = document.getElementById('categoriesChart').getContext('2d');
        new Chart(categoriesCtx, {
            type: 'doughnut',
            data: {
                labels: categoriesData.map(item => item.label),
                datasets: [{
                    data: categoriesData.map(item => item.value),
                    backgroundColor: categoriesData.map(item => item.color),
                    hoverOffset: 4
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    // Products Chart
    const productsData = [
        {% for prod_data in top_products[:10] %}
        {
            label: '{{ prod_data.product.name[:20] }}{% if prod_data.product.name|length > 20 %}...{% endif %}',
            value: {{ prod_data.revenue }}
        },
        {% endfor %}
    ];
    
    if (productsData.length > 0) {
        const productsCtx = document.getElementById('productsChart').getContext('2d');
        new Chart(productsCtx, {
            type: 'bar',
            data: {
                labels: productsData.map(item => item.label),
                datasets: [{
                    label: 'Chiffre d\'affaires (€)',
                    data: productsData.map(item => item.value),
                    backgroundColor: 'rgba(78, 115, 223, 0.8)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + ' €';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
});

function exportCategoriesReport(format) {
    // Get current filters
    const period = document.getElementById('period').value;
    
    // Build URL with parameters
    let url = new URL(`${window.location.origin}/reports/categories/export`);
    url.searchParams.set('period', period);
    url.searchParams.set('format', format);
    
    if (period === 'custom') {
        const startDate = document.querySelector('input[name="start_date"]').value;
        const endDate = document.querySelector('input[name="end_date"]').value;
        url.searchParams.set('start_date', startDate);
        url.searchParams.set('end_date', endDate);
    }
    
    // Trigger download
    window.location.href = url.toString();
}
</script>
{% endblock %}

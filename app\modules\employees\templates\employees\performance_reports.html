{% extends "employees/base_hr.html" %}

{% block title %}Rapports de Performance{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-chart-line me-2"></i>Rapports de Performance
    </h1>
    <div>
        <button class="btn btn-outline-success me-2" onclick="exportPerformanceData()">
            <i class="fas fa-download me-1"></i>Exporter
        </button>
        <a href="{{ url_for('employees.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour
        </a>
    </div>
</div>

<!-- Statistiques générales -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_evaluations }}</h4>
                        <p class="mb-0">Évaluations Totales</p>
                    </div>
                    <i class="fas fa-clipboard-list fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ finalized_evaluations }}</h4>
                        <p class="mb-0">Finalisées</p>
                    </div>
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ acknowledged_evaluations }}</h4>
                        <p class="mb-0">Reconnues</p>
                    </div>
                    <i class="fas fa-user-check fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ "%.1f"|format(avg_overall or 0) }}/5</h4>
                        <p class="mb-0">Score Moyen</p>
                    </div>
                    <i class="fas fa-star fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Moyennes par critère -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Moyennes par Critère
                </h5>
            </div>
            <div class="card-body">
                <canvas id="criteriaChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-trophy me-2"></i>Top Performers
                </h5>
            </div>
            <div class="card-body">
                {% if top_performers %}
                <div class="list-group list-group-flush">
                    {% for performance in top_performers %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                {{ performance.employee.first_name[0] }}{{ performance.employee.last_name[0] }}
                            </div>
                            <div>
                                <strong>{{ performance.employee.full_name }}</strong><br>
                                <small class="text-muted">{{ performance.employee.position }}</small>
                            </div>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-success fs-6">{{ "%.1f"|format(performance.overall_score or 0) }}/5</span><br>
                            <small class="text-muted">{{ performance.evaluation_date | datetime_auto('date') }}</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted text-center">Aucune évaluation finalisée</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Employés nécessitant une attention -->
{% if needs_attention %}
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-exclamation-triangle me-2 text-warning"></i>Employés Nécessitant une Attention
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            {% for performance in needs_attention %}
            <div class="col-md-6 mb-3">
                <div class="card border-warning">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                    {{ performance.employee.first_name[0] }}{{ performance.employee.last_name[0] }}
                                </div>
                                <div>
                                    <strong>{{ performance.employee.full_name }}</strong><br>
                                    <small class="text-muted">{{ performance.employee.position }}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-warning fs-6">{{ "%.1f"|format(performance.overall_score or 0) }}/5</span><br>
                                <small class="text-muted">{{ performance.evaluation_date | datetime_auto('date') }}</small>
                            </div>
                        </div>
                        {% if performance.areas_for_improvement %}
                        <div class="mt-2">
                            <small class="text-muted">Axes d'amélioration:</small>
                            <p class="small mb-0">{{ performance.areas_for_improvement[:100] }}{% if performance.areas_for_improvement|length > 100 %}...{% endif %}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Détails des critères -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>Détails des Critères
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-sm">
                    <tr>
                        <td><i class="fas fa-clock me-2 text-primary"></i>Ponctualité</td>
                        <td><strong>{{ "%.1f"|format(avg_punctuality or 0) }}/5</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-star me-2 text-success"></i>Qualité du travail</td>
                        <td><strong>{{ "%.1f"|format(avg_quality or 0) }}/5</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-users me-2 text-info"></i>Travail d'équipe</td>
                        <td><strong>{{ "%.1f"|format(avg_teamwork or 0) }}/5</strong></td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-sm">
                    <tr>
                        <td><i class="fas fa-comments me-2 text-warning"></i>Communication</td>
                        <td><strong>{{ "%.1f"|format(avg_communication or 0) }}/5</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-lightbulb me-2 text-danger"></i>Initiative</td>
                        <td><strong>{{ "%.1f"|format(avg_initiative or 0) }}/5</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-handshake me-2 text-primary"></i>Service client</td>
                        <td><strong>{{ "%.1f"|format(avg_customer_service or 0) }}/5</strong></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
    font-weight: bold;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Graphique des critères
const criteriaCtx = document.getElementById('criteriaChart').getContext('2d');
const criteriaChart = new Chart(criteriaCtx, {
    type: 'radar',
    data: {
        labels: ['Ponctualité', 'Qualité', 'Équipe', 'Communication', 'Initiative', 'Service Client'],
        datasets: [{
            label: 'Moyenne',
            data: [
                {{ avg_punctuality or 0 }},
                {{ avg_quality or 0 }},
                {{ avg_teamwork or 0 }},
                {{ avg_communication or 0 }},
                {{ avg_initiative or 0 }},
                {{ avg_customer_service or 0 }}
            ],
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            r: {
                beginAtZero: true,
                max: 5
            }
        }
    }
});

function exportPerformanceData() {
    window.open('/employees/performance/export', '_blank');
}
</script>
{% endblock %}

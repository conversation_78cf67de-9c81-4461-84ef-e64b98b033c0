<!-- En-tête avec nom de l'article -->
<div class="mb-3">
    <h6 class="text-primary">
        <i class="fas fa-history"></i>
        Historique de : <strong>{{ item.name }}</strong>
        <small class="text-muted">({{ item.unit }})</small>
    </h6>
</div>

{% if movements %}
<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead class="table-light">
            <tr>
                <th>Date</th>
                <th>Type</th>
                <th>Motif</th>
                <th>Quantité</th>
                <th>Stock avant</th>
                <th>Stock après</th>
                <th>Notes</th>
                <th>Utilisateur</th>
            </tr>
        </thead>
        <tbody>
            {% for movement in movements %}
            <tr>
                <td>{{ movement.created_at | datetime_auto('full') }}</td>
                <td>
                    {% if movement.type == 'in' %}
                    <span class="badge bg-success">
                        <i class="fas fa-arrow-up"></i> Entrée
                    </span>
                    {% else %}
                    <span class="badge bg-danger">
                        <i class="fas fa-arrow-down"></i> Sortie
                    </span>
                    {% endif %}
                </td>
                <td>
                    <span class="badge bg-secondary">
                        {% if movement.reason == 'purchase' %}
                            <i class="fas fa-shopping-cart"></i> Achat
                        {% elif movement.reason == 'sale' %}
                            <i class="fas fa-cash-register"></i> Vente
                        {% elif movement.reason == 'loss' %}
                            <i class="fas fa-exclamation-triangle"></i> Perte
                        {% elif movement.reason == 'adjustment' %}
                            <i class="fas fa-edit"></i> Ajustement
                        {% elif movement.reason == 'recipe' %}
                            <i class="fas fa-utensils"></i> Recette
                        {% else %}
                            <i class="fas fa-question"></i> {{ movement.reason|title }}
                        {% endif %}
                    </span>
                </td>
                <td>
                    {% if movement.type == 'in' %}
                    <span class="text-success fw-bold">+{{ movement.quantity }} {{ item.unit }}</span>
                    {% else %}
                    <span class="text-danger fw-bold">-{{ movement.quantity }} {{ item.unit }}</span>
                    {% endif %}
                </td>
                <td>{{ movement.previous_quantity }} {{ item.unit }}</td>
                <td>{{ movement.new_quantity }} {{ item.unit }}</td>
                <td>
                    {% if movement.notes %}
                        <span class="text-muted" title="{{ movement.notes }}">
                            {{ movement.notes[:50] }}{% if movement.notes|length > 50 %}...{% endif %}
                        </span>
                    {% else %}
                        <span class="text-muted">-</span>
                    {% endif %}
                </td>
                <td>
                    {% if movement.user %}
                        <span class="badge bg-info">{{ movement.user.username }}</span>
                    {% else %}
                        <span class="text-muted">Système</span>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Pagination -->
{% if pagination and pagination.pages > 1 %}
<nav aria-label="Pagination de l'historique">
    <ul class="pagination pagination-sm justify-content-center">
        {% if pagination.has_prev %}
        <li class="page-item">
            <a class="page-link" href="#" onclick="loadHistoryPage({{ item_id }}, {{ pagination.prev_num }})">Précédent</a>
        </li>
        {% endif %}

        {% for page_num in pagination.iter_pages() %}
            {% if page_num %}
                {% if page_num != pagination.page %}
                <li class="page-item">
                    <a class="page-link" href="#" onclick="loadHistoryPage({{ item_id }}, {{ page_num }})">{{ page_num }}</a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}

        {% if pagination.has_next %}
        <li class="page-item">
            <a class="page-link" href="#" onclick="loadHistoryPage({{ item_id }}, {{ pagination.next_num }})">Suivant</a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<p class="text-muted">Aucun mouvement trouvé pour cet article.</p>
{% endif %}